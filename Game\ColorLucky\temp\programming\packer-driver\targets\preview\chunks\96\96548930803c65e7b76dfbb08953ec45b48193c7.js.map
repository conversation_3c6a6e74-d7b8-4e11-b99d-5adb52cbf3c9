{"version": 3, "sources": ["file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/BallsPanel.ts"], "names": ["_decorator", "Component", "instantiate", "Label", "Node", "Prefab", "Animation", "tween", "UITransform", "easing", "NumberUtils", "BallItem", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eventManager", "ccclass", "property", "BallsPanel", "numberSpawner", "dataSource", "numbers", "setDataSource", "data", "console", "log", "displayBalls", "delay", "numberIdx", "schedule", "number", "currentNumber", "push", "showBigNumberAnimation", "onBigNumberAnimationend", "bind", "scheduleOnce", "emit", "hole", "node", "<PERSON><PERSON><PERSON><PERSON><PERSON>y<PERSON><PERSON>", "children", "fadeIn", "getChildByName", "active", "ani", "getComponent", "play", "updateBarAnimation", "hideAllBalls", "for<PERSON>ach", "colors", "getColorCount", "gap", "length", "Math", "max", "bar", "barItem", "idx", "countNode", "string", "toString", "targetNode", "to", "height", "linear", "start", "onEnable", "onDisable", "unscheduleAllCallbacks", "onLoad", "Array", "from", "_", "i", "setNumber", "<PERSON><PERSON><PERSON><PERSON>", "update", "deltaTime"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;;AACxFC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;;4BAGjBgB,U,WADZF,OAAO,CAAC,YAAD,C,UAKHC,QAAQ,CAACV,MAAD,C,UAIRU,QAAQ,CAACX,IAAD,C,2BATb,MACaY,UADb,SACgCf,SADhC,CAC0C;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAY9BgB,aAZ8B,GAYC,IAZD;AAAA,eAc9BC,UAd8B,GAcZ,IAdY;AAAA,eAgB9BC,OAhB8B,GAgBL,EAhBK;AAAA;;AAkBtCC,QAAAA,aAAa,CAACC,IAAD,EAAYJ,aAAZ,EAA0C;AACnDK,UAAAA,OAAO,CAACC,GAAR,CAAY,0BAAZ,EAAwCF,IAAxC;AACA,eAAKH,UAAL,GAAkBG,IAAlB;AACA,eAAKJ,aAAL,GAAqBA,aAArB;AACH;;AAEOO,QAAAA,YAAY,GAAG;AACnB;AACA,eAAKL,OAAL,GAAe,EAAf;AACA,cAAMM,KAAK,GAAG,GAAd;AACA,cAAIC,SAAS,GAAG,CAAhB,CAJmB,CAKnB;;AACA,eAAKC,QAAL,CAAc,MAAM;AAEhB;AACA,gBAAMC,MAAM,GAAG,KAAKV,UAAL,CAAgBW,aAAhB,CAA8BH,SAA9B,CAAf;AACA,iBAAKP,OAAL,CAAaW,IAAb,CAAkBF,MAAlB;AAEAN,YAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ,EAAsBK,MAAtB;AAEA,iBAAKX,aAAL,CAAmBc,sBAAnB,CAA0CH,MAA1C,EAAkD,KAAKI,uBAAL,CAA6BC,IAA7B,CAAkC,IAAlC,EAAwCL,MAAxC,CAAlD;AAEAF,YAAAA,SAAS,GAVO,CAYhB;;AAEA,gBAAIA,SAAS,IAAI,EAAjB,EAAqB;AACjB,mBAAKQ,YAAL,CAAkB,MAAM;AACpB;AAAA;AAAA,kDAAaC,IAAb,CAAkB,mBAAlB,EADoB,CAEpB;AACH,eAHD,EAGG,CAHH;AAIH;AACJ,WApBD,EAoBGV,KApBH,EAoBU,EApBV,EAoBcA,KApBd;AAqBH;;AAEOO,QAAAA,uBAAuB,CAACJ,MAAD,EAAiB;AAC5C;AACA,cAAMQ,IAAI,GAAG,KAAKC,IAAL,CAAUC,cAAV,CAAyB,SAAzB,EAAoCC,QAApC,CAA6CX,MAAM,GAAG,CAAtD,CAAb;AACA;AAAA;AAAA,wCAAWY,MAAX,CAAkBJ,IAAI,CAACK,cAAL,CAAoB,MAApB,CAAlB,EAA+C,GAA/C;AACAL,UAAAA,IAAI,CAACK,cAAL,CAAoB,KAApB,EAA2BC,MAA3B,GAAoC,IAApC;AAEA,cAAMC,GAAG,GAAGP,IAAI,CAACK,cAAL,CAAoB,KAApB,EAA2BG,YAA3B,CAAwCtC,SAAxC,CAAZ;AACAqC,UAAAA,GAAG,CAACE,IAAJ,CAAS,MAAT;AACA,eAAKC,kBAAL;AACH;;AAGOC,QAAAA,YAAY,GAAG;AACnB,eAAKV,IAAL,CAAUC,cAAV,CAAyB,SAAzB,EAAoCC,QAApC,CAA6CS,OAA7C,CAAqDX,IAAI,IAAI;AACzDA,YAAAA,IAAI,CAACI,cAAL,CAAoB,MAApB,EAA4BC,MAA5B,GAAqC,KAArC;AACAL,YAAAA,IAAI,CAACI,cAAL,CAAoB,KAApB,EAA2BC,MAA3B,GAAoC,KAApC;AACH,WAHD;AAIH;;AAGOI,QAAAA,kBAAkB,GAAG;AAEzB,cAAMG,MAAM,GAAG;AAAA;AAAA,0CAAYC,aAAZ,CAA0B,KAAK/B,OAA/B,CAAf;AAEA,cAAIgC,GAAG,GAAG,IAAV,CAJyB,CAMzB;;AACA,cAAI,KAAKhC,OAAL,CAAaiC,MAAb,IAAuB,EAA3B,EAA+B;AAC3BD,YAAAA,GAAG,GAAG,MAAME,IAAI,CAACC,GAAL,CAAS,GAAGL,MAAZ,CAAZ;AACH,WAFD,MAEO,IAAI,KAAK9B,OAAL,CAAaiC,MAAb,GAAsB,EAA1B,EAA8B;AACjCD,YAAAA,GAAG,GAAG,OAAQ,KAAK,KAAKhC,OAAL,CAAaiC,MAAnB,GAA6BC,IAAI,CAACC,GAAL,CAAS,GAAGL,MAAZ,CAApC,CAAN;AACH;;AAED,eAAKM,GAAL,CAAShB,QAAT,CAAkBS,OAAlB,CAA0B,CAACQ,OAAD,EAAUC,GAAV,KAAkB;AACxC,gBAAMC,SAAS,GAAGF,OAAO,CAACf,cAAR,CAAuB,MAAvB,EAA+BG,YAA/B,CAA4CzC,KAA5C,CAAlB;AACAuD,YAAAA,SAAS,CAACC,MAAV,GAAmBV,MAAM,CAACQ,GAAD,CAAN,CAAYG,QAAZ,EAAnB;AAEA,gBAAMC,UAAU,GAAGL,OAAO,CAACf,cAAR,CAAuB,OAAvB,EAAgCG,YAAhC,CAA6CpC,WAA7C,CAAnB,CAJwC,CAMxC;;AACAD,YAAAA,KAAK,CAACsD,UAAD,CAAL,CACKC,EADL,CACQ,GADR,EACa;AAAEC,cAAAA,MAAM,EAAEd,MAAM,CAACQ,GAAD,CAAN,GAAcN;AAAxB,aADb,EAC4C;AACpC1C,cAAAA,MAAM,EAAEA,MAAM,CAACuD;AADqB,aAD5C,EAIKC,KAJL;AAKH,WAZD;AAaH;;AAGSC,QAAAA,QAAQ,GAAS;AACvB;AAAA;AAAA,4CAAa/B,IAAb,CAAkB,kBAAlB;AACA,eAAKX,YAAL;AACH;;AAES2C,QAAAA,SAAS,GAAS;AACxB,eAAKhD,OAAL,GAAe,EAAf;AACA,eAAKiD,sBAAL;AACA,eAAKtB,kBAAL;AACA,eAAKC,YAAL;AACH;;AAEDsB,QAAAA,MAAM,GAAG;AACL/C,UAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ;AACA+C,UAAAA,KAAK,CAACC,IAAN,CAAW;AAAEnB,YAAAA,MAAM,EAAE;AAAV,WAAX,EAA2BJ,OAA3B,CAAmC,CAACwB,CAAD,EAAIC,CAAJ,KAAU;AACzC,gBAAMrC,IAAI,GAAGlC,WAAW,CAAC,KAAKkC,IAAN,CAAxB;AACAA,YAAAA,IAAI,CAACK,cAAL,CAAoB,MAApB,EAA4BG,YAA5B,CAAyCzC,KAAzC,EAAgDwD,MAAhD,GAAyD,CAACc,CAAC,GAAG,CAAL,EAAQb,QAAR,EAAzD;AACAxB,YAAAA,IAAI,CAACK,cAAL,CAAoB,MAApB,EAA4BG,YAA5B;AAAA;AAAA,sCAAmD8B,SAAnD,CAA6DD,CAAC,GAAG,CAAjE;AACA,iBAAKpC,IAAL,CAAUC,cAAV,CAAyB,SAAzB,EAAoCqC,QAApC,CAA6CvC,IAA7C;AACH,WALD;AAMH;;AAEDwC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AA9HqC,O;;;;;iBAKjB,I;;;;;;;iBAID,I", "sourcesContent": ["import { _decorator, Component, instantiate, Label, Node, Prefab, Animation, tween, UITransform, easing } from 'cc';\r\nimport { NumberUtils } from '../../framework/utils/NumberUtils';\r\nimport { BallItem } from '../perfabs/BallItem';\r\nimport { TweenUtils } from '../../framework/utils/TweenUtils';\r\nimport { eventManager } from '../../framework/event/EventManager';\r\nimport { NumberSpawner } from '../NumberSpawner';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('BallsPanel')\r\nexport class BallsPanel extends Component {\r\n\r\n\r\n\r\n    @property(Prefab)\r\n    private hole: Node = null;\r\n\r\n\r\n    @property(Node)\r\n    private bar: Node = null;\r\n\r\n\r\n    private numberSpawner: NumberSpawner = null;\r\n\r\n    private dataSource: any = null;\r\n\r\n    private numbers: Array<number> = [];\r\n\r\n    setDataSource(data: any, numberSpawner: NumberSpawner) {\r\n        console.log(\"BallsPanel setDataSource\", data);\r\n        this.dataSource = data;\r\n        this.numberSpawner = numberSpawner;\r\n    }\r\n\r\n    private displayBalls() {\r\n        // this.dataSource.currentNumber = NumberUtils.getRandomNumbers(20);\r\n        this.numbers = [];\r\n        const delay = 1.5;\r\n        let numberIdx = 0;\r\n        // this.numberSpawner.playBounceAnimation();\r\n        this.schedule(() => {\r\n\r\n            //小球数字\r\n            const number = this.dataSource.currentNumber[numberIdx];\r\n            this.numbers.push(number);\r\n\r\n            console.log(\"number\", number);\r\n\r\n            this.numberSpawner.showBigNumberAnimation(number, this.onBigNumberAnimationend.bind(this, number));\r\n\r\n            numberIdx++;\r\n\r\n            // this.updateBarAnimation();\r\n\r\n            if (numberIdx >= 19) {\r\n                this.scheduleOnce(() => {\r\n                    eventManager.emit('BallsPanel_Finish');\r\n                    // TweenUtils.fadeOut(this.node);\r\n                }, 5);\r\n            }\r\n        }, delay, 19, delay);\r\n    }\r\n\r\n    private onBigNumberAnimationend(number: number) {\r\n        //小球坑位\r\n        const hole = this.node.getChildByPath('numbers').children[number - 1];\r\n        TweenUtils.fadeIn(hole.getChildByName('ball'), 0.1);\r\n        hole.getChildByName('ani').active = true;\r\n\r\n        const ani = hole.getChildByName('ani').getComponent(Animation);\r\n        ani.play('star');\r\n        this.updateBarAnimation();\r\n    }\r\n\r\n\r\n    private hideAllBalls() {\r\n        this.node.getChildByPath('numbers').children.forEach(node => {\r\n            node.getChildByName('ball').active = false;\r\n            node.getChildByName('ani').active = false;\r\n        });\r\n    }\r\n\r\n\r\n    private updateBarAnimation() {\r\n\r\n        const colors = NumberUtils.getColorCount(this.numbers);\r\n\r\n        let gap = 12.5;\r\n\r\n        //当小球个数>10个时所有柱体按照比例增加\r\n        if (this.numbers.length == 20) {\r\n            gap = 250 / Math.max(...colors);\r\n        } else if (this.numbers.length > 10) {\r\n            gap = 250 / ((20 - this.numbers.length) + Math.max(...colors));\r\n        }\r\n\r\n        this.bar.children.forEach((barItem, idx) => {\r\n            const countNode = barItem.getChildByName('text').getComponent(Label);\r\n            countNode.string = colors[idx].toString();\r\n\r\n            const targetNode = barItem.getChildByName('value').getComponent(UITransform);\r\n\r\n            // 创建 Tween 动画\r\n            tween(targetNode)\r\n                .to(0.1, { height: colors[idx] * gap }, {\r\n                    easing: easing.linear,\r\n                })\r\n                .start();\r\n        });\r\n    }\r\n\r\n\r\n    protected onEnable(): void {\r\n        eventManager.emit('BallsPanel_Start');\r\n        this.displayBalls();\r\n    }\r\n\r\n    protected onDisable(): void {\r\n        this.numbers = [];\r\n        this.unscheduleAllCallbacks();\r\n        this.updateBarAnimation();\r\n        this.hideAllBalls();\r\n    }\r\n\r\n    onLoad() {\r\n        console.log(\"BallsPanel start\");\r\n        Array.from({ length: 80 }).forEach((_, i) => {\r\n            const hole = instantiate(this.hole);\r\n            hole.getChildByName('text').getComponent(Label).string = (i + 1).toString();\r\n            hole.getChildByName('ball').getComponent(BallItem).setNumber(i + 1);\r\n            this.node.getChildByPath('numbers').addChild(hole);\r\n        });\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n\r\n    }\r\n}\r\n\r\n\r\n"]}