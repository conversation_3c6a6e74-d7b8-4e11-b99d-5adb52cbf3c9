enum EColor {
    Blue = 0,
    Orange = 1,
    Purple = 2,
    Green = 3
}

export class NumberUtils {


    public static getTopAndBottomNumbers(data: any[]) {

        // 创建一个映射来存储每个 number 的出现次数
        const numberCounts: { [key: number]: number } = {};

        for (let i = 1; i <=80; i++) { 
            numberCounts[i] = 0;
        }

        // 遍历数据并统计每个 number 的出现次数
        data.forEach(num => {
            if (num !== undefined) {
                numberCounts[num] = (numberCounts[num] || 0) + 1;
            }
        });

        // 将统计结果转换为数组并排序
        const sortedNumbers = Object.entries(numberCounts).map(([num, count]) => ({
            number: parseInt(num),
            count,
        }));

        // 按照出现次数排序
        sortedNumbers.sort((a, b) => b.count - a.count);

        // 获取出现次数最多的 5 个
        const top10 = sortedNumbers.slice(0, 10);

        // 获取出现次数最少的 5 个（排除 count 为 0 的） 翻转
        const bottom10 = sortedNumbers
            .slice(-10).reverse()
        

        return { top10, bottom10 };
    }


    /**
     * 根据数字获取颜色
     * @param number 
     * @returns 
     */
    public static getColor(number: number): EColor {
        const colors = [EColor.Blue, EColor.Orange, EColor.Purple, EColor.Green];
        return colors[(number - 1) % 4];
    }

    /**
     * 计算每种颜色出现的次数
     * @param numbers 
     * @returns 
     */
    public static getColorCount(numbers: Array<number>) {
        return numbers.reduce((acc, cur) => {
            acc[NumberUtils.getColor(cur)]++;
            return acc;
        }, [0, 0, 0, 0]);
    }

    /**
     * 从1-80中随机生成N个不重复的整数
     */
    public static getRandomNumbers(count: number = 20): number[] {
        if (count < 0 || count > 80) {
            throw new Error("Count must be between 0 and 80");
        }

        // 创建1到80的数组
        const numbers = Array.from({ length: 80 }, (_, i) => i + 1);

        // Fisher-Yates洗牌算法
        for (let i = numbers.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [numbers[i], numbers[j]] = [numbers[j], numbers[i]];
        }

        // 返回前count个元素
        return numbers.slice(0, count);
    }

    public static getRandomNumber(): number {
        return NumberUtils.getRandomNumbers(1)[0];
    }



}