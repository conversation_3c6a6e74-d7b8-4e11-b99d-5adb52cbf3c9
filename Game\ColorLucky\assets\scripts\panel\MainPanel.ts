import { _decorator, Component, instantiate, Label, Node, Prefab, Tween } from 'cc';
import { BallItem } from '../perfabs/BallItem';
import { NumberUtils } from '../../framework/utils/NumberUtils';
import { TweenUtils } from '../../framework/utils/TweenUtils';
const { ccclass, property } = _decorator;

@ccclass('MainPanel')
export class MainPanel extends Component {


    @property(Prefab)
    private smallBall: Node = null;

    private smallBalls: Array<Node> = [];
    
    private dataSource: any = null;

    start() {

    }


    setDataSource(data: any) {
        this.dataSource = data;
    }

    // public renderRounds(recents: Array<{round: number, result: Array<number>, roundTime: number}>) { 
    //     this.node.getChildByName('rounds').children.forEach((item, idx) => {
            
    //         if (!recents[idx]) { 
    //             item.active = false;
    //             return;
    //         }
    //         const round = recents[idx];
            
    //         item.getChildByName('sn').getComponent(Label).string = `SN-${round.round}`;

    //         this.setColorCount(round.result, item.getChildByName('block'));


    //         round.result.forEach((number, numIdx) => {

    //             const parentNode = item.getChildByName('balls');

    //             if (parentNode.children.length >= 20) {
    //                 parentNode.children[numIdx].getComponent(BallItem).setNumber(number);
    //             } else { 
    //                 let ballNode = instantiate(this.smallBall);
    //                 ballNode.getComponent(BallItem).setNumber(number);

    //                 parentNode.addChild(ballNode);
    //                 this.smallBalls.push(ballNode);
    //             }
    //         });
    //     });
    // }


    /**
     * 显示每个回合的小球数据
     */
    private displayRounds() {
        this.node.getChildByName('rounds').children.forEach((item, idx) => { 
            const round = this.dataSource.recentResult[idx];

            // 设置SN
            item.getChildByName('sn').getComponent(Label).string = `SN-${round.round}`;

            // 设置颜色Winner
            this.setColorCount(round.result, item.getChildByName('block'));

            // 设置球
            const parentNode = item.getChildByName('balls');
            round.result.forEach((number, numIdx) => {
                if (parentNode.children.length >= 20) {
                    parentNode.children[numIdx].getComponent(BallItem).setNumber(number);
                } else { 
                    let ballNode = instantiate(this.smallBall);
                    ballNode.getComponent(BallItem).setNumber(number);

                    parentNode.addChild(ballNode);
                    this.smallBalls.push(ballNode);
                }
            });
        });
    }


    /**
     * 设置颜色Winner
     * @param numbers 
     * @param block 
     */
    private setColorCount(numbers: Array<number>, block: Node) { 
        const colorCount = NumberUtils.getColorCount(numbers);

        const max = Math.max(...colorCount);

        let maxIdx = colorCount.indexOf(max);

        if (colorCount.filter(item => item === max).length > 1) { 
            maxIdx = -1;
        }

        block.children.forEach((item, idx) => {
            item.getChildByName('count').getComponent(Label).string = colorCount[idx].toString();
            item.getChildByName('crown').active = (idx == maxIdx);
            item.active = true;
        });
    }



    protected onEnable(): void {

        this.displayRounds();

        this.schedule(() => {
            this.showAnimation();
        }, 10);
    }

   
    /**
     * 主界面所有数字的动画
     */
    showAnimation() {
        const totalDuration = 2.5;
        const duration = totalDuration / this.smallBalls.length;

        this.smallBalls.forEach((ball, idx) => {
            //计算出每个小球的延迟时间
            const delay = (idx * duration);
            TweenUtils.scaleAndRestore(ball, 1.2, 0.05, delay);
        });
    }

    protected onDisable(): void {
        this.unscheduleAllCallbacks();
        this.smallBalls.forEach(item => { 
            Tween.stopAllByTarget(item);
        });
    }
}


