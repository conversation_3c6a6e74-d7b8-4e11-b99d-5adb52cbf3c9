{"modules": {"cce:/internal/x/cc": {"mTimestamp": 19992.582000000402, "chunkId": "93ba276ea7b26ffcdc433fab14afc1ed6f05647b", "imports": [{"value": "cce:/internal/x/cc-fu/2d", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 14}, "end": {"line": 1, "column": 40}}}, {"value": "cce:/internal/x/cc-fu/sorting", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 14}, "end": {"line": 2, "column": 45}}}, {"value": "cce:/internal/x/cc-fu/animation", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 14}, "end": {"line": 3, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/base", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 14}, "end": {"line": 4, "column": 42}}}, {"value": "cce:/internal/x/cc-fu/custom-pipeline", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 14}, "end": {"line": 5, "column": 53}}}, {"value": "cce:/internal/x/cc-fu/gfx-webgl", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 14}, "end": {"line": 6, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/gfx-webgl2", "resolved": "__unresolved_6", "loc": {"start": {"line": 7, "column": 14}, "end": {"line": 7, "column": 48}}}, {"value": "cce:/internal/x/cc-fu/gfx-webgpu", "resolved": "__unresolved_7", "loc": {"start": {"line": 8, "column": 14}, "end": {"line": 8, "column": 48}}}, {"value": "cce:/internal/x/cc-fu/profiler", "resolved": "__unresolved_8", "loc": {"start": {"line": 9, "column": 14}, "end": {"line": 9, "column": 46}}}, {"value": "cce:/internal/x/cc-fu/tween", "resolved": "__unresolved_9", "loc": {"start": {"line": 10, "column": 14}, "end": {"line": 10, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/ui", "resolved": "__unresolved_10", "loc": {"start": {"line": 11, "column": 14}, "end": {"line": 11, "column": 40}}}], "type": "esm", "resolutions": [{"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/2d"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/sorting"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/animation"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/base"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/custom-pipeline"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/gfx-webgl"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/gfx-webgl2"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/gfx-webgpu"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/profiler"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/tween"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/ui"}, "messages": []}]}, "cce:/internal/x/prerequisite-imports": {"mTimestamp": 15238544.0066, "chunkId": "6d8fd2b0177941b032ddc0733af48a561fb60657", "imports": [{"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.4/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts", "resolved": "__unresolved_0", "loc": {"start": {"line": 4, "column": 7}, "end": {"line": 4, "column": 155}}}, {"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.4/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts", "resolved": "__unresolved_1", "loc": {"start": {"line": 5, "column": 7}, "end": {"line": 5, "column": 152}}}, {"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.4/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts", "resolved": "__unresolved_2", "loc": {"start": {"line": 6, "column": 7}, "end": {"line": 6, "column": 146}}}, {"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.4/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts", "resolved": "__unresolved_3", "loc": {"start": {"line": 7, "column": 7}, "end": {"line": 7, "column": 139}}}, {"value": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/event/EventManager.ts", "resolved": "__unresolved_4", "loc": {"start": {"line": 8, "column": 7}, "end": {"line": 8, "column": 96}}}, {"value": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/logger/logger.ts", "resolved": "__unresolved_5", "loc": {"start": {"line": 9, "column": 7}, "end": {"line": 9, "column": 91}}}, {"value": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/network/websocket.ts", "resolved": "__unresolved_6", "loc": {"start": {"line": 10, "column": 7}, "end": {"line": 10, "column": 95}}}, {"value": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/ui/PageAdapter.ts", "resolved": "__unresolved_7", "loc": {"start": {"line": 11, "column": 7}, "end": {"line": 11, "column": 92}}}, {"value": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/FormatUtils.ts", "resolved": "__unresolved_8", "loc": {"start": {"line": 12, "column": 7}, "end": {"line": 12, "column": 95}}}, {"value": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/NumberUtils.ts", "resolved": "__unresolved_9", "loc": {"start": {"line": 13, "column": 7}, "end": {"line": 13, "column": 95}}}, {"value": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/TweenUtils.ts", "resolved": "__unresolved_10", "loc": {"start": {"line": 14, "column": 7}, "end": {"line": 14, "column": 94}}}, {"value": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/ColorLucky.ts", "resolved": "__unresolved_11", "loc": {"start": {"line": 15, "column": 7}, "end": {"line": 15, "column": 86}}}, {"value": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/Countdown.ts", "resolved": "__unresolved_12", "loc": {"start": {"line": 16, "column": 7}, "end": {"line": 16, "column": 85}}}, {"value": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/NumberSpawner.ts", "resolved": "__unresolved_13", "loc": {"start": {"line": 17, "column": 7}, "end": {"line": 17, "column": 89}}}, {"value": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/BallsPanel.ts", "resolved": "__unresolved_14", "loc": {"start": {"line": 18, "column": 7}, "end": {"line": 18, "column": 92}}}, {"value": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/ColorPanel.ts", "resolved": "__unresolved_15", "loc": {"start": {"line": 19, "column": 7}, "end": {"line": 19, "column": 92}}}, {"value": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/MainPanel.ts", "resolved": "__unresolved_16", "loc": {"start": {"line": 20, "column": 7}, "end": {"line": 20, "column": 91}}}, {"value": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/SuggestPanel.ts", "resolved": "__unresolved_17", "loc": {"start": {"line": 21, "column": 7}, "end": {"line": 21, "column": 94}}}, {"value": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/BallItem.ts", "resolved": "__unresolved_18", "loc": {"start": {"line": 22, "column": 7}, "end": {"line": 22, "column": 92}}}, {"value": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/Led.ts", "resolved": "__unresolved_19", "loc": {"start": {"line": 23, "column": 7}, "end": {"line": 23, "column": 87}}}, {"value": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/Pearl.ts", "resolved": "__unresolved_20", "loc": {"start": {"line": 24, "column": 7}, "end": {"line": 24, "column": 89}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.4/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.4/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.4/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.4/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/event/EventManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/logger/logger.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/network/websocket.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/ui/PageAdapter.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/FormatUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/NumberUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/TweenUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/ColorLucky.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/Countdown.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/NumberSpawner.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/BallsPanel.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/ColorPanel.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/MainPanel.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/SuggestPanel.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/BallItem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/Led.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/Pearl.ts"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.4/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts": {"mTimestamp": {"mtime": 1733241306443.2288, "uuid": "de1c2107-70c8-4021-8459-6399f24d01c6"}, "chunkId": "d6137fe08bf966a053351bb405928e5a6390befd", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 35, "column": 7}, "end": {"line": 35, "column": 11}}}, {"value": "cc/env", "loc": {"start": {"line": 37, "column": 23}, "end": {"line": 37, "column": 31}}}, {"value": "./builtin-pipeline-types", "resolved": "__unresolved_1", "loc": {"start": {"line": 43, "column": 7}, "end": {"line": 43, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.4/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts"}, "messages": []}]}, "cce:/internal/code-quality/cr.mjs": {"mTimestamp": 1755054719438, "chunkId": "6a5019a719a9014c047e67aa1cf34453ab8392ce", "imports": [], "type": "esm", "resolutions": []}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.4/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts": {"mTimestamp": {"mtime": 1733241306445.0908, "uuid": "cbf30902-517f-40dc-af90-a550bac27cf1"}, "chunkId": "b349769b96cc4c1dff92d5d6f941366e27a02c6b", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 31, "column": 41}, "end": {"line": 31, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.4/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts": {"mTimestamp": {"mtime": 1733241306446.0903, "uuid": "ff9b0199-ce04-4cfe-86cc-6c719f08d6e4"}, "chunkId": "59b7958b47eec6168615a6469581f8f48e26cb00", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc/env", "loc": {"start": {"line": 25, "column": 30}, "end": {"line": 25, "column": 38}}}, {"value": "cc", "loc": {"start": {"line": 45, "column": 7}, "end": {"line": 45, "column": 11}}}, {"value": "./builtin-pipeline-types", "resolved": "__unresolved_1", "loc": {"start": {"line": 50, "column": 7}, "end": {"line": 50, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.4/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.4/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts": {"mTimestamp": {"mtime": 1733241306680.9075, "uuid": "b2bd1fa7-8d7c-49c5-a158-df29a6d3a594"}, "chunkId": "9758c8bdbb4fd247ebce274d105abb8265561b82", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 220}, "end": {"line": 1, "column": 224}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/utils/FormatUtils.ts": {"mTimestamp": {"mtime": 1747586574119.5398, "uuid": "47ace2f6-43a8-4781-be84-bbfb46b126bc"}, "chunkId": "e28a9fdeb2723ba9ab61b870f8e3acfe6725174a", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/utils/NumberUtils.ts": {"mTimestamp": {"mtime": 1749146114129.6006, "uuid": "f082b063-98b8-4c80-80a2-d5d245ec9142"}, "chunkId": "90a32a5e1f0bfc0ee08b59174bd025bb0d44b9d2", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/utils/TweenUtils.ts": {"mTimestamp": {"mtime": 1750605986689.3574, "uuid": "9d5dd2aa-c0bc-4dda-ae87-84c666541cac"}, "chunkId": "81d86f9079f635a2b18adaf945301d35744bcbb8", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 93}, "end": {"line": 1, "column": 97}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/ColorLucky.ts": {"mTimestamp": {"mtime": 1750614023387.4302, "uuid": "34681a91-069c-4d9a-8f18-7927b3e94a92"}, "chunkId": "1173539ec84a0205b18ca5cf66fd53d5f626bb2f", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 146}, "end": {"line": 1, "column": 150}}}, {"value": "../framework/utils/TweenUtils", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 58}}}, {"value": "./panel/BallsPanel", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 27}, "end": {"line": 3, "column": 47}}}, {"value": "../framework/network/websocket", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 59}}}, {"value": "./panel/MainPanel", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 26}, "end": {"line": 5, "column": 45}}}, {"value": "./panel/SuggestPanel", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 29}, "end": {"line": 6, "column": 51}}}, {"value": "./NumberSpawner", "resolved": "__unresolved_6", "loc": {"start": {"line": 7, "column": 30}, "end": {"line": 7, "column": 47}}}, {"value": "../framework/event/EventManager", "resolved": "__unresolved_7", "loc": {"start": {"line": 8, "column": 29}, "end": {"line": 8, "column": 62}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/utils/TweenUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/panel/BallsPanel.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/network/websocket.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/panel/MainPanel.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/panel/SuggestPanel.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/NumberSpawner.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/event/EventManager.ts"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/perfabs/Led.ts": {"mTimestamp": {"mtime": 1747556305603.455, "uuid": "05b06d99-606e-4f11-8da1-d924e69faed7"}, "chunkId": "3cb630cb484d424568cfebdc7340b56fd7a8a18a", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}, {"value": "../../framework/utils/TweenUtils", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 61}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/utils/TweenUtils.ts"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/Countdown.ts": {"mTimestamp": {"mtime": 1747581369977.6267, "uuid": "d27700d1-6f8b-4209-a50b-c7e8aacfbb92"}, "chunkId": "38fcb9df0e41ff758ad48927b514736621143953", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 78}, "end": {"line": 1, "column": 82}}}, {"value": "./perfabs/Led", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 20}, "end": {"line": 2, "column": 35}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/perfabs/Led.ts"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/panel/ColorPanel.ts": {"mTimestamp": {"mtime": 1750602000971.6353, "uuid": "e28c7a4d-4a23-46ae-a497-fc43b52b41ba"}, "chunkId": "5cb9fd445bf37d94dd499fde4f1303b5005e08e8", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 83}, "end": {"line": 1, "column": 87}}}, {"value": "../../framework/utils/TweenUtils", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 61}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/utils/TweenUtils.ts"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/panel/SuggestPanel.ts": {"mTimestamp": {"mtime": 1750601846614.883, "uuid": "bea152a4-5ada-49f9-8ec0-569652ed3ae7"}, "chunkId": "deaaf513a2e13b2fa398e812213bbfd46a46ab3d", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 79}, "end": {"line": 1, "column": 83}}}, {"value": "../../framework/utils/TweenUtils", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 61}}}, {"value": "../../framework/utils/NumberUtils", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 63}}}, {"value": "../perfabs/BallItem", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": 46}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/utils/TweenUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/utils/NumberUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/perfabs/BallItem.ts"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/panel/MainPanel.ts": {"mTimestamp": {"mtime": 1750601563482.768, "uuid": "75775892-2c61-4a12-b86c-c210f52a79b1"}, "chunkId": "bf28ece37064b1f9cfea977152d0d01af0fa2c93", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 79}, "end": {"line": 1, "column": 83}}}, {"value": "../perfabs/BallItem", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": 46}}}, {"value": "../../framework/utils/NumberUtils", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 63}}}, {"value": "../../framework/utils/TweenUtils", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 61}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/perfabs/BallItem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/utils/NumberUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/utils/TweenUtils.ts"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/panel/RoundPanel.ts": {"mTimestamp": {"mtime": 1747583019937.485, "uuid": "4dfe1a38-c22a-4658-8c42-0a42c3f30d1f"}, "chunkId": "20e7ae05403ee7321cb974d53a51b905ade7e2f6", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 65}, "end": {"line": 1, "column": 69}}}, {"value": "../perfabs/BallItem", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": 46}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/perfabs/BallItem.ts"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/perfabs/BallItem.ts": {"mTimestamp": {"mtime": 1747677470429.3618, "uuid": "941772af-d841-49ed-a60b-d0e52578db54"}, "chunkId": "f0416b410ad89044dc07cb2adfb042c7358cb566", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 59}, "end": {"line": 1, "column": 63}}}, {"value": "../../framework/utils/NumberUtils", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 63}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/utils/NumberUtils.ts"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/BallPanel.ts": {"mTimestamp": {"mtime": 1747674052628.0703, "uuid": "c414b97e-3589-4a10-9b96-e9e22d41135d"}, "chunkId": "018e66744ea541699daef7732df048a282240fdb", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/panel/BallPanel.ts": {"mTimestamp": {"mtime": 1747677689959.5024, "uuid": "c414b97e-3589-4a10-9b96-e9e22d41135d"}, "chunkId": "8ea5e6b905ef5352508bab39eb91bcd4fb71e64a", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 83}, "end": {"line": 1, "column": 87}}}, {"value": "../perfabs/BallItem", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 46}}}, {"value": "../../framework/utils/TweenUtils", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 61}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/perfabs/BallItem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/utils/TweenUtils.ts"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/panel/BallsPanel.ts": {"mTimestamp": {"mtime": 1750613845752.7383, "uuid": "c414b97e-3589-4a10-9b96-e9e22d41135d"}, "chunkId": "e16b30e2b896993ccfdbc6b9a186ab7bd179d7ca", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 111}, "end": {"line": 1, "column": 115}}}, {"value": "../../framework/utils/NumberUtils", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 63}}}, {"value": "../perfabs/BallItem", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 46}}}, {"value": "../../framework/utils/TweenUtils", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 61}}}, {"value": "../../framework/event/EventManager", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": 65}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/utils/NumberUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/perfabs/BallItem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/utils/TweenUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/event/EventManager.ts"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/network/websocket.ts": {"mTimestamp": {"mtime": 1749137728329.946, "uuid": "77fb7f90-c6ce-4475-91e5-9ebd01cb4f0c"}, "chunkId": "68781fe499ba3625ea883fabfeb8f304ae3c9578", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "../logger/logger", "resolved": "__unresolved_1", "loc": {"start": {"line": 1, "column": 19}, "end": {"line": 1, "column": 37}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/logger/logger.ts"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/logger/logger.ts": {"mTimestamp": {"mtime": 1746816362262.4243, "uuid": "672e8344-fe45-4fd1-8929-221bb69c48fe"}, "chunkId": "4ee69fec45334dd012473bc31ba45226828431dd", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/NumberSpawner.ts": {"mTimestamp": {"mtime": 1750614637759.245, "uuid": "9e017f85-82f9-4caa-bac4-************"}, "chunkId": "371ec4c0c89b3f873f1b7fb01dd92c7832a9769e", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 135}, "end": {"line": 1, "column": 139}}}, {"value": "./perfabs/Led", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 20}, "end": {"line": 2, "column": 35}}}, {"value": "../framework/utils/TweenUtils", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 27}, "end": {"line": 3, "column": 58}}}, {"value": "../framework/utils/FormatUtils", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 60}}}, {"value": "./perfabs/BallItem", "resolved": "__unresolved_4", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 45}}}, {"value": "../framework/event/EventManager", "resolved": "__unresolved_5", "loc": {"start": {"line": 7, "column": 29}, "end": {"line": 7, "column": 62}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/perfabs/Led.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/utils/TweenUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/utils/FormatUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/scripts/perfabs/BallItem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/event/EventManager.ts"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/event/EventManager.ts": {"mTimestamp": {"mtime": 1750607364543.5818, "uuid": "7d3d556d-438e-4bd2-8055-863a7559e273"}, "chunkId": "063757f8ba86fc36dadfa07702262557bcde59a6", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 28}, "end": {"line": 1, "column": 32}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/ui/NewComponent.ts": {"mTimestamp": {"mtime": 1750640420398.2742, "uuid": "1a98c142-d2a3-4502-ac88-30a43c903b2f"}, "chunkId": "24b957e943d52d82a09a1351d7017d2c186904d3", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///H:/%E4%BA%91%E5%B8%86%E7%BD%91%E7%BB%9C/Magic/04-Development/02-Code/ColorLucky/assets/framework/ui/PageAdapter.ts": {"mTimestamp": {"mtime": 1750642501834.2336, "uuid": "1a98c142-d2a3-4502-ac88-30a43c903b2f"}, "chunkId": "ab6c21ae4571b160bb43cc11f6f069c9703a318c", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 13, "column": 129}, "end": {"line": 13, "column": 133}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/event/EventManager.ts": {"mTimestamp": {"mtime": 1750607364543.5818, "uuid": "7d3d556d-438e-4bd2-8055-863a7559e273"}, "chunkId": "6a3a509c48f44722f954c80a9e38828e2d91a213", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 28}, "end": {"line": 1, "column": 32}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/logger/logger.ts": {"mTimestamp": {"mtime": 1746816362262.4243, "uuid": "672e8344-fe45-4fd1-8929-221bb69c48fe"}, "chunkId": "af4ce00279c6e3fee2074fb0afef4fa6312e55a8", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/network/websocket.ts": {"mTimestamp": {"mtime": 1749137728329.946, "uuid": "77fb7f90-c6ce-4475-91e5-9ebd01cb4f0c"}, "chunkId": "b0cd80a77114195d5be1d1dc3fff05e173788d78", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "../logger/logger", "resolved": "__unresolved_1", "loc": {"start": {"line": 1, "column": 19}, "end": {"line": 1, "column": 37}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/logger/logger.ts"}, "messages": []}]}, "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/ui/PageAdapter.ts": {"mTimestamp": {"mtime": 1750642501834.2336, "uuid": "1a98c142-d2a3-4502-ac88-30a43c903b2f"}, "chunkId": "827e26fe31664c1a52bc36b4d7801ff463e6e749", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 13, "column": 129}, "end": {"line": 13, "column": 133}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/FormatUtils.ts": {"mTimestamp": {"mtime": 1747586574119.5398, "uuid": "47ace2f6-43a8-4781-be84-bbfb46b126bc"}, "chunkId": "df8cb8ef1a376d7c239c6fd037ef8df5ab142f50", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/NumberUtils.ts": {"mTimestamp": {"mtime": 1749146114129.6006, "uuid": "f082b063-98b8-4c80-80a2-d5d245ec9142"}, "chunkId": "5a0bf945da2170ec23565b4ae6dbdb115d39cde3", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/TweenUtils.ts": {"mTimestamp": {"mtime": 1750605986689.3574, "uuid": "9d5dd2aa-c0bc-4dda-ae87-84c666541cac"}, "chunkId": "ce12fc4621385b5bd480e557f97802c450bbb496", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 93}, "end": {"line": 1, "column": 97}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/ColorLucky.ts": {"mTimestamp": {"mtime": 1750614023387.4302, "uuid": "34681a91-069c-4d9a-8f18-7927b3e94a92"}, "chunkId": "d68ad0f67ff44c497cbc89b4fe8786284836c8a5", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 146}, "end": {"line": 1, "column": 150}}}, {"value": "../framework/utils/TweenUtils", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 58}}}, {"value": "./panel/BallsPanel", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 27}, "end": {"line": 3, "column": 47}}}, {"value": "../framework/network/websocket", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 59}}}, {"value": "./panel/MainPanel", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 26}, "end": {"line": 5, "column": 45}}}, {"value": "./panel/SuggestPanel", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 29}, "end": {"line": 6, "column": 51}}}, {"value": "./NumberSpawner", "resolved": "__unresolved_6", "loc": {"start": {"line": 7, "column": 30}, "end": {"line": 7, "column": 47}}}, {"value": "../framework/event/EventManager", "resolved": "__unresolved_7", "loc": {"start": {"line": 8, "column": 29}, "end": {"line": 8, "column": 62}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/TweenUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/BallsPanel.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/network/websocket.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/MainPanel.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/SuggestPanel.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/NumberSpawner.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/event/EventManager.ts"}, "messages": []}]}, "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/BallsPanel.ts": {"mTimestamp": {"mtime": 1755069955597.4204, "uuid": "c414b97e-3589-4a10-9b96-e9e22d41135d"}, "chunkId": "96548930803c65e7b76dfbb08953ec45b48193c7", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 111}, "end": {"line": 1, "column": 115}}}, {"value": "../../framework/utils/NumberUtils", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 63}}}, {"value": "../perfabs/BallItem", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 46}}}, {"value": "../../framework/utils/TweenUtils", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 61}}}, {"value": "../../framework/event/EventManager", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": 65}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/NumberUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/BallItem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/TweenUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/event/EventManager.ts"}, "messages": []}]}, "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/BallItem.ts": {"mTimestamp": {"mtime": 1747677470429.3618, "uuid": "941772af-d841-49ed-a60b-d0e52578db54"}, "chunkId": "6f7328cf533fa1ce0c9d7a44dc94755010317737", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 59}, "end": {"line": 1, "column": 63}}}, {"value": "../../framework/utils/NumberUtils", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 63}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/NumberUtils.ts"}, "messages": []}]}, "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/MainPanel.ts": {"mTimestamp": {"mtime": 1750601563482.768, "uuid": "75775892-2c61-4a12-b86c-c210f52a79b1"}, "chunkId": "63805ed9d049d92708685d5472c234ef244a133a", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 79}, "end": {"line": 1, "column": 83}}}, {"value": "../perfabs/BallItem", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": 46}}}, {"value": "../../framework/utils/NumberUtils", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 63}}}, {"value": "../../framework/utils/TweenUtils", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 61}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/BallItem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/NumberUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/TweenUtils.ts"}, "messages": []}]}, "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/SuggestPanel.ts": {"mTimestamp": {"mtime": 1750601846614.883, "uuid": "bea152a4-5ada-49f9-8ec0-569652ed3ae7"}, "chunkId": "1c419d131be0d10fc7bc75027e94dc076144d01f", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 79}, "end": {"line": 1, "column": 83}}}, {"value": "../../framework/utils/TweenUtils", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 61}}}, {"value": "../../framework/utils/NumberUtils", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 63}}}, {"value": "../perfabs/BallItem", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": 46}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/TweenUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/NumberUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/BallItem.ts"}, "messages": []}]}, "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/NumberSpawner.ts": {"mTimestamp": {"mtime": 1755069014468.5935, "uuid": "9e017f85-82f9-4caa-bac4-************"}, "chunkId": "46b833dd3a3ec51ee19ad67ad6fba72e08580eb7", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 135}, "end": {"line": 1, "column": 139}}}, {"value": "../framework/utils/TweenUtils", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 27}, "end": {"line": 3, "column": 58}}}, {"value": "../framework/utils/FormatUtils", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 60}}}, {"value": "../framework/utils/NumberUtils", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 60}}}, {"value": "./perfabs/BallItem", "resolved": "__unresolved_4", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 45}}}, {"value": "../framework/event/EventManager", "resolved": "__unresolved_5", "loc": {"start": {"line": 7, "column": 29}, "end": {"line": 7, "column": 62}}}, {"value": "./perfabs/Pearl", "resolved": "__unresolved_6", "loc": {"start": {"line": 8, "column": 22}, "end": {"line": 8, "column": 39}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/TweenUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/FormatUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/NumberUtils.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/BallItem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/event/EventManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/Pearl.ts"}, "messages": []}]}, "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/Led.ts": {"mTimestamp": {"mtime": 1747556305603.455, "uuid": "05b06d99-606e-4f11-8da1-d924e69faed7"}, "chunkId": "99be5df03cbb1f0090ea1c22d0e99ad588f96080", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}, {"value": "../../framework/utils/TweenUtils", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 61}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/TweenUtils.ts"}, "messages": []}]}, "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/Countdown.ts": {"mTimestamp": {"mtime": 1747581369977.6267, "uuid": "d27700d1-6f8b-4209-a50b-c7e8aacfbb92"}, "chunkId": "ea01b4e629d9be7c64cabfde2c1ca86fa7c5c6d7", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 78}, "end": {"line": 1, "column": 82}}}, {"value": "./perfabs/Led", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 20}, "end": {"line": 2, "column": 35}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/Led.ts"}, "messages": []}]}, "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/ColorPanel.ts": {"mTimestamp": {"mtime": 1750602000971.6353, "uuid": "e28c7a4d-4a23-46ae-a497-fc43b52b41ba"}, "chunkId": "564b4a2a4bf59f18018afd98f8c0808188574b43", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 83}, "end": {"line": 1, "column": 87}}}, {"value": "../../framework/utils/TweenUtils", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 61}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/TweenUtils.ts"}, "messages": []}]}, "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/Led-001.ts": {"mTimestamp": {"mtime": 1747556305603.455, "uuid": "ce7aaf9f-1d26-449b-a889-c7c37ee794b9"}, "chunkId": "f2a4503ef3028e9eaf71f6c2fcae25ccf12637d9", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}, {"value": "../../framework/utils/TweenUtils", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 61}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/TweenUtils.ts"}, "messages": []}]}, "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/Pearl.ts": {"mTimestamp": {"mtime": 1755062867086.3298, "uuid": "ce7aaf9f-1d26-449b-a889-c7c37ee794b9"}, "chunkId": "e138bddf128dfb655e2c91b580726d35e3e093a7", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}, {"value": "../../framework/utils/TweenUtils", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 61}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/TweenUtils.ts"}, "messages": []}]}}}