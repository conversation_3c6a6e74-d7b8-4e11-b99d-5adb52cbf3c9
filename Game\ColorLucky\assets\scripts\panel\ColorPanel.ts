import { _decorator, Component, Label, Node, tween, UITransform, Vec2, Vec3 } from 'cc';
import { TweenUtils } from '../../framework/utils/TweenUtils';
const { ccclass, property } = _decorator;

@ccclass('ColorPanel')
export class ColorPanel extends Component {

    private tips: Node = null;
    private tipsInitX: number = -125;

    private crown: Node = null;
    private crownInitX: number = -90;

    private maxHeight: number = 250;


    start() {
        this.tips = this.node.getChildByName('tips');
        this.tipsInitX = this.tips.position.x;
        this.crown = this.node.getChildByName('crown');
        this.crownInitX = this.crown.position.x;
    }

    private updateBar(data = []) {
        const max = Math.max(...data);
        const maxs = data.filter(num => num == max);
        const maxIndex = data.indexOf(max);

        const texts = ['Blue', 'Orange', 'Purple', 'Green'];

        TweenUtils.fadeIn(this.tips);
        //设置提示框的位置
        this.tips.setPosition(this.tipsInitX + (maxIndex * 105), this.tips.position.y);

        const label = this.tips.getChildByName('text').getComponent(Label);
        //设置提示框的文字 有两个数字一样 则单独处理
        if (maxs.length == 1) {
            label.string = texts[maxIndex];
            label.fontSize = 30;

            //设置皇冠的位置
            TweenUtils.fadeIn(this.crown);
            this.crown.setPosition(this.crownInitX + (maxIndex * 105), this.crown.position.y);
        } else {
            //隐藏皇冠
            TweenUtils.fadeOut(this.crown);
            label.string = 'Two,no winner';
            // label.fontSize = 25;
        }

        this.node.getChildByName('bar').children.forEach((item, idx) => {
            const height = this.maxHeight / max * data[idx];

            const node = item.getChildByName('value');
            TweenUtils.tweenHeight(node, height, 0.5);

            const label = item.getChildByName('text');
            TweenUtils.tweenNumber(label, 0, data[idx], 0.5);

            item.getChildByName('text').getComponent(Label).string = data[idx].toString();
        });
    }

    protected onEnable(): void {
        const list = [
            [9, 1, 6, 4],
            [6, 6, 5, 3], //Two,no winner
            [4, 6, 5, 5],
            [6, 3, 8, 3],
            [2, 5, 6, 7]
        ];

        list.forEach((data, idx) => {
            this.scheduleOnce(() => {
                this.updateBar(data);
            }, idx * 3);
        });
    }

    protected onDisable(): void {
        this.unscheduleAllCallbacks();
    }

    update(deltaTime: number) {

    }
}


