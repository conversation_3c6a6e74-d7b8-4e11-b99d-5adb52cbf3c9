2025-8-13 11:12:10-debug: start **** info
2025-8-13 11:12:12-log: Cannot access game frame or container.
2025-8-13 11:12:13-debug: asset-db:require-engine-code (3169ms)
2025-8-13 11:12:13-log: meshopt wasm decoder initialized
2025-8-13 11:12:13-log: [box2d]:box2d wasm lib loaded.
2025-8-13 11:12:13-log: [bullet]:bullet wasm lib loaded.
2025-8-13 11:12:14-log: Cocos Creator v3.8.4
2025-8-13 11:12:14-log: Using legacy pipeline
2025-8-13 11:12:14-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:28.16MB, end 81.24MB, increase: 53.08MB
2025-8-13 11:12:18-debug: [Assets Memory track]: asset-db-plugin-register: builder start:88.34MB, end 162.45MB, increase: 74.11MB
2025-8-13 11:12:18-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:162.69MB, end 165.78MB, increase: 3.09MB
2025-8-13 11:12:15-debug: [Assets Memory track]: asset-db-plugin-register: programming start:82.09MB, end 88.31MB, increase: 6.22MB
2025-8-13 11:12:18-debug: [Assets Memory track]: asset-db-plugin-register: project start:81.96MB, end 165.85MB, increase: 83.89MB
2025-8-13 11:12:14-log: Forward render pipeline initialized.
2025-8-13 11:12:18-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:81.26MB, end 165.88MB, increase: 84.62MB
2025-8-13 11:12:18-debug: asset-db:worker-init: initPlugin (3614ms)
2025-8-13 11:12:18-debug: [Assets Memory track]: asset-db:worker-init start:28.15MB, end 160.74MB, increase: 132.59MB
2025-8-13 11:12:18-debug: Run asset db hook engine-extends:beforePreStart success!
2025-8-13 11:12:18-debug: Run asset db hook engine-extends:beforePreStart ...
2025-8-13 11:12:18-debug: Run asset db hook programming:beforePreStart ...
2025-8-13 11:12:18-debug: Run asset db hook programming:beforePreStart success!
2025-8-13 11:12:18-debug: asset-db:worker-init (8632ms)
2025-8-13 11:12:18-debug: asset-db-hook-programming-beforePreStart (318ms)
2025-8-13 11:12:18-debug: asset-db-hook-engine-extends-beforePreStart (317ms)
2025-8-13 11:12:19-debug: Preimport db internal success
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\framework
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scenes
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\framework\event
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\framework\logger
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\framework\network
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\framework\ui
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\framework\utils
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\panel
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\perfabs
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\led
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\ColorLucky.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\framework\logger\logger.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\Countdown.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\framework\event\EventManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\NumberSpawner.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\framework\network\websocket.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\framework\utils\NumberUtils.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\framework\ui\PageAdapter.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\framework\utils\TweenUtils.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\framework\utils\FormatUtils.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\panel\BallsPanel.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\panel\ColorPanel.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\panel\MainPanel.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\panel\SuggestPanel.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\perfabs\BallItem.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\perfabs\Led.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:19-debug: Preimport db assets success
2025-8-13 11:12:19-debug: Run asset db hook programming:afterPreStart ...
2025-8-13 11:12:19-debug: starting packer-driver...
2025-8-13 11:12:24-debug: initialize scripting environment...
2025-8-13 11:12:24-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-13 11:12:24-debug: Run asset db hook engine-extends:afterPreStart success!
2025-8-13 11:12:24-debug: [Assets Memory track]: asset-db:worker-init: preStart start:160.76MB, end 164.94MB, increase: 4.19MB
2025-8-13 11:12:24-debug: Start up the 'internal' database...
2025-8-13 11:12:24-debug: Run asset db hook programming:afterPreStart success!
2025-8-13 11:12:24-debug: Run asset db hook engine-extends:afterPreStart ...
2025-8-13 11:12:24-debug: asset-db-hook-programming-afterPreStart (5791ms)
2025-8-13 11:12:24-debug: asset-db:worker-effect-data-processing (873ms)
2025-8-13 11:12:24-debug: asset-db-hook-engine-extends-afterPreStart (875ms)
2025-8-13 11:12:25-debug: Start up the 'assets' database...
2025-8-13 11:12:25-debug: asset-db:worker-startup-database[internal] (6567ms)
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\ball.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\bar.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\ballsPanel.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\colorPanel.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\bigBall.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\hole.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\led.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\mainPanel.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\number.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\round.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\spawn.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\suggestPanel.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scenes\ColorLucky.scene
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\animation.anim
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce.anim
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star.anim
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\color.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bg.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\logo.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\keno.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\lucky.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\keno.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\color.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\logo.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\keno.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\color.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\logo.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\lucky.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\lucky.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00019.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00017.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00015.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00021.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00023.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00019.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00017.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00015.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00019.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00017.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00015.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00023.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00023.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00021.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00021.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00025.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00027.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00029.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00031.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00025.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00033.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00027.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00025.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00029.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00027.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00031.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00029.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00031.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00033.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00033.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00035.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00037.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00039.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00041.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00043.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00035.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00037.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00039.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00035.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00037.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00039.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00041.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00041.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00043.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00043.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00045.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00047.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00049.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00051.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00053.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00045.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00047.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00045.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00049.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00051.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00047.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00049.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00053.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00051.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00053.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00055.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00059.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00057.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00061.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00063.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00055.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00059.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00057.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00061.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00055.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00059.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00057.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00063.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00061.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:25-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00063.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00065.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00069.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00067.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00071.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00073.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00065.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00067.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00069.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00065.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00067.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00069.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00071.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00073.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00071.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00073.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00077.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00075.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00079.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00081.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00083.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00075.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00077.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00079.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00077.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00075.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00081.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00083.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00079.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00081.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00083.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00087.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00085.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00091.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00089.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00093.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00087.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00085.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00091.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00087.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00089.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00085.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00091.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00089.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00093.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00093.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00097.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00095.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00099.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00000.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00001.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00095.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00097.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00099.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00000.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00095.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00099.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00000.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00001.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\bounce\合成 1_00097.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00001.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00002.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00003.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00005.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00007.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00003.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00005.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00002.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00009.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00003.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00007.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00005.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00002.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00007.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00009.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00009.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00011.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00015.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00013.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00017.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00011.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00015.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00019.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00013.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00011.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00015.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00017.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00013.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00017.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00019.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00019.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00021.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\blue.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\blue_large.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\green.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00021.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\green_large.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\blue.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\animations\star\合成 1_00021.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\blue_large.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\blue.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\green.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\blue_large.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\green.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\green_large.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\green_large.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\orange.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\hole.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\orange_large.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\purple.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\purple_large.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\hole.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\orange.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\orange_large.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\hole.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\purple.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\orange.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\orange_large.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\purple.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\purple_large.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\ball\purple_large.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\bg.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\bg_dark.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\blue.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\<EMAIL>
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\green.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\bg_dark.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\blue.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\<EMAIL>@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\bg_dark.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\blue.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\<EMAIL>@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\green.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\green.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\<EMAIL>
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\orange.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\<EMAIL>
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\purple.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\<EMAIL>
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\<EMAIL>@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\orange.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\purple.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\<EMAIL>@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\<EMAIL>@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\orange.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\purple.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\<EMAIL>@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\<EMAIL>@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\<EMAIL>@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\tips.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\blue.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\crown.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\green.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\fire.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\tips.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\blue.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\crown.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\bar\tips.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\blue.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\fire.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\green.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\crown.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\fire.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\green.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\orange.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\purple.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\snowflake.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\led\led.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\led\led_light.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\orange.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\purple.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\orange.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\purple.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\snowflake.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\led\led_light.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\led\led.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\block\snowflake.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\led\led_light.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\led\led.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:26-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\led\round.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\led\编组@2x.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\1.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\led\路径@2x (2).png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\10.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\led\round.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\led\round.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\led\路径@2x (2).png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\led\编组@2x.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\1.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\led\路径@2x (2).png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\1.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\10.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\led\编组@2x.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\10.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\2.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\3.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\4.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\5.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\6.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\3.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\4.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\6.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\5.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\3.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\4.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\6.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\5.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\7.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\8.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\9.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\7.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\8.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\9.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\7.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\9.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\number\8.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:12:27-debug: lazy register asset handler *
2025-8-13 11:12:27-debug: lazy register asset handler directory
2025-8-13 11:12:27-debug: lazy register asset handler spine-data
2025-8-13 11:12:27-debug: lazy register asset handler dragonbones
2025-8-13 11:12:27-debug: lazy register asset handler dragonbones-atlas
2025-8-13 11:12:27-debug: lazy register asset handler terrain
2025-8-13 11:12:27-debug: lazy register asset handler javascript
2025-8-13 11:12:27-debug: lazy register asset handler typescript
2025-8-13 11:12:27-debug: lazy register asset handler scene
2025-8-13 11:12:27-debug: lazy register asset handler prefab
2025-8-13 11:12:27-debug: lazy register asset handler sprite-frame
2025-8-13 11:12:27-debug: lazy register asset handler tiled-map
2025-8-13 11:12:27-debug: lazy register asset handler buffer
2025-8-13 11:12:27-debug: lazy register asset handler image
2025-8-13 11:12:27-debug: lazy register asset handler sign-image
2025-8-13 11:12:27-debug: lazy register asset handler texture
2025-8-13 11:12:27-debug: lazy register asset handler alpha-image
2025-8-13 11:12:27-debug: lazy register asset handler texture-cube
2025-8-13 11:12:27-debug: lazy register asset handler erp-texture-cube
2025-8-13 11:12:27-debug: lazy register asset handler texture-cube-face
2025-8-13 11:12:27-debug: lazy register asset handler rt-sprite-frame
2025-8-13 11:12:27-debug: lazy register asset handler gltf
2025-8-13 11:12:27-debug: lazy register asset handler gltf-mesh
2025-8-13 11:12:27-debug: lazy register asset handler gltf-animation
2025-8-13 11:12:27-debug: lazy register asset handler gltf-skeleton
2025-8-13 11:12:27-debug: lazy register asset handler gltf-material
2025-8-13 11:12:27-debug: lazy register asset handler gltf-scene
2025-8-13 11:12:27-debug: lazy register asset handler gltf-embeded-image
2025-8-13 11:12:27-debug: lazy register asset handler fbx
2025-8-13 11:12:27-debug: lazy register asset handler material
2025-8-13 11:12:27-debug: lazy register asset handler physics-material
2025-8-13 11:12:27-debug: lazy register asset handler effect
2025-8-13 11:12:27-debug: lazy register asset handler effect-header
2025-8-13 11:12:27-debug: lazy register asset handler audio-clip
2025-8-13 11:12:27-debug: lazy register asset handler text
2025-8-13 11:12:27-debug: lazy register asset handler render-texture
2025-8-13 11:12:27-debug: lazy register asset handler json
2025-8-13 11:12:27-debug: lazy register asset handler animation-clip
2025-8-13 11:12:27-debug: lazy register asset handler animation-graph-variant
2025-8-13 11:12:27-debug: lazy register asset handler animation-graph
2025-8-13 11:12:27-debug: lazy register asset handler animation-mask
2025-8-13 11:12:27-debug: lazy register asset handler ttf-font
2025-8-13 11:12:27-debug: lazy register asset handler bitmap-font
2025-8-13 11:12:27-debug: lazy register asset handler particle
2025-8-13 11:12:27-debug: lazy register asset handler sprite-atlas
2025-8-13 11:12:27-debug: lazy register asset handler render-pipeline
2025-8-13 11:12:27-debug: lazy register asset handler label-atlas
2025-8-13 11:12:27-debug: lazy register asset handler auto-atlas
2025-8-13 11:12:27-debug: lazy register asset handler instantiation-mesh
2025-8-13 11:12:27-debug: lazy register asset handler instantiation-material
2025-8-13 11:12:27-debug: lazy register asset handler render-stage
2025-8-13 11:12:27-debug: lazy register asset handler render-flow
2025-8-13 11:12:27-debug: lazy register asset handler instantiation-skeleton
2025-8-13 11:12:27-debug: lazy register asset handler instantiation-animation
2025-8-13 11:12:27-debug: lazy register asset handler video-clip
2025-8-13 11:12:27-debug: asset-db:worker-startup-database[assets] (8159ms)
2025-8-13 11:12:27-debug: asset-db:ready (26471ms)
2025-8-13 11:12:27-debug: asset-db:start-database (8600ms)
2025-8-13 11:12:27-debug: init worker message success
2025-8-13 11:12:27-debug: programming:execute-script (7ms)
2025-8-13 11:12:28-debug: [Build Memory track]: builder:worker-init start:150.40MB, end 164.66MB, increase: 14.26MB
2025-8-13 11:12:28-debug: builder:worker-init (1058ms)
2025-8-13 11:12:58-debug: refresh db internal success
2025-8-13 11:12:58-debug: refresh db assets success
2025-8-13 11:12:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:12:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:12:58-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:12:58-debug: asset-db:refresh-all-database (271ms)
2025-8-13 11:12:58-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:13:25-debug: refresh db internal success
2025-8-13 11:13:25-debug: refresh db assets success
2025-8-13 11:13:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:13:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:13:25-debug: asset-db:refresh-all-database (214ms)
2025-8-13 11:13:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 11:13:25-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:13:41-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\组件 6 - 1 1x、PNG (1).png...
2025-8-13 11:13:41-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\组件 6 - 1 1x、PNG (1).png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:13:41-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\组件 6 - 1 1x、PNG (1).png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:13:41-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\组件 6 - 1 1x、PNG (1).png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:13:41-debug: refresh db internal success
2025-8-13 11:13:41-log: 资源数据库已锁定，资源操作(bound autoRefreshAssetLazy)将会延迟响应，请稍侯
2025-8-13 11:13:41-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images success
2025-8-13 11:13:41-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:13:41-debug: refresh db assets success
2025-8-13 11:13:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:13:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:13:41-debug: asset-db:refresh-all-database (351ms)
2025-8-13 11:13:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 11:13:41-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:13:47-debug: start move asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\组件 6 - 1 1x、PNG (1).png -> E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\new_bg.png...
2025-8-13 11:13:47-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\new_bg.png...
2025-8-13 11:13:47-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\new_bg.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:13:47-debug: %cReImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\new_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:13:47-debug: %cReImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\new_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:13:47-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images success
2025-8-13 11:13:47-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images...
2025-8-13 11:13:47-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:13:47-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\resources success
2025-8-13 11:13:47-debug: move asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\组件 6 - 1 1x、PNG (1).png -> E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\new_bg.png success
2025-8-13 11:13:57-debug: refresh db internal success
2025-8-13 11:13:57-debug: refresh db assets success
2025-8-13 11:13:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:13:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:13:57-debug: asset-db:refresh-all-database (187ms)
2025-8-13 11:13:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 11:13:57-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 11:14:03-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scenes\ColorLucky.scene
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:14:03-debug: asset-db:reimport-assetbb178f2a-b8ec-40c5-b625-fa77e0041139 (18ms)
2025-8-13 11:14:10-debug: refresh db internal success
2025-8-13 11:14:10-debug: refresh db assets success
2025-8-13 11:14:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:14:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:14:10-debug: asset-db:refresh-all-database (178ms)
2025-8-13 11:14:10-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:14:10-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 11:14:14-debug: Query all assets info in project
2025-8-13 11:14:14-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 11:14:14-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 11:14:14-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:14:14-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 11:14:14-debug:   Number of all scenes: 1
2025-8-13 11:14:14-debug:   Number of all scripts: 20
2025-8-13 11:14:14-debug:   Number of other assets: 637
2025-8-13 11:14:14-debug: // ---- build task 查询 Asset Bundle ---- (54ms)
2025-8-13 11:14:14-debug: run build task 查询 Asset Bundle success in 54 ms√, progress: 5%
2025-8-13 11:14:14-debug: [Build Memory track]: 查询 Asset Bundle start:160.42MB, end 161.69MB, increase: 1.27MB
2025-8-13 11:14:14-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 11:14:14-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:14:14-debug: // ---- build task 查询 Asset Bundle ---- (11ms)
2025-8-13 11:14:14-debug: run build task 查询 Asset Bundle success in 11 ms√, progress: 10%
2025-8-13 11:14:14-debug: [Build Memory track]: 查询 Asset Bundle start:161.72MB, end 161.86MB, increase: 140.32KB
2025-8-13 11:14:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 11:14:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:14:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 11:14:14-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-13 11:14:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:161.90MB, end 161.92MB, increase: 28.01KB
2025-8-13 11:14:14-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 11:14:14-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 11:14:14-debug: // ---- build task 填充脚本数据到 settings.json ---- (8ms)
2025-8-13 11:14:14-debug: run build task 填充脚本数据到 settings.json success in 8 ms√, progress: 13%
2025-8-13 11:14:14-debug: [Build Memory track]: 填充脚本数据到 settings.json start:161.96MB, end 161.99MB, increase: 28.11KB
2025-8-13 11:14:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 11:14:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:14:14-debug: run build task 整理部分构建选项内数据到 settings.json success in 21 ms√, progress: 15%
2025-8-13 11:14:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:162.02MB, end 162.16MB, increase: 142.05KB
2025-8-13 11:14:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (21ms)
2025-8-13 11:14:22-debug: refresh db internal success
2025-8-13 11:14:22-debug: Query all assets info in project
2025-8-13 11:14:22-debug: refresh db assets success
2025-8-13 11:14:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:14:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:14:22-debug: asset-db:refresh-all-database (412ms)
2025-8-13 11:14:22-debug: asset-db:worker-effect-data-processing (8ms)
2025-8-13 11:14:22-debug: asset-db-hook-engine-extends-afterRefresh (12ms)
2025-8-13 11:14:22-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 11:14:22-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 11:14:22-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:14:22-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 11:14:22-debug:   Number of all scenes: 1
2025-8-13 11:14:22-debug:   Number of other assets: 637
2025-8-13 11:14:22-debug:   Number of all scripts: 20
2025-8-13 11:14:22-debug: // ---- build task 查询 Asset Bundle ---- (33ms)
2025-8-13 11:14:22-debug: run build task 查询 Asset Bundle success in 33 ms√, progress: 5%
2025-8-13 11:14:22-debug: [Build Memory track]: 查询 Asset Bundle start:162.22MB, end 162.81MB, increase: 601.46KB
2025-8-13 11:14:22-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 11:14:22-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:14:22-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-8-13 11:14:22-debug: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-8-13 11:14:22-debug: [Build Memory track]: 查询 Asset Bundle start:162.84MB, end 162.08MB, increase: -784.41KB
2025-8-13 11:14:22-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 11:14:22-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:14:22-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (17ms)
2025-8-13 11:14:22-debug: run build task 整理部分构建选项内数据到 settings.json success in 17 ms√, progress: 12%
2025-8-13 11:14:22-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:162.11MB, end 162.15MB, increase: 33.62KB
2025-8-13 11:14:22-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 11:14:22-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 11:14:22-debug: // ---- build task 填充脚本数据到 settings.json ---- (3ms)
2025-8-13 11:14:22-debug: run build task 填充脚本数据到 settings.json success in 3 ms√, progress: 13%
2025-8-13 11:14:22-debug: [Build Memory track]: 填充脚本数据到 settings.json start:162.18MB, end 162.21MB, increase: 32.46KB
2025-8-13 11:14:22-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 11:14:22-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:14:22-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (7ms)
2025-8-13 11:14:22-debug: run build task 整理部分构建选项内数据到 settings.json success in 7 ms√, progress: 15%
2025-8-13 11:14:22-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:162.25MB, end 162.38MB, increase: 133.74KB
2025-8-13 11:14:24-debug: refresh db internal success
2025-8-13 11:14:24-debug: refresh db assets success
2025-8-13 11:14:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:14:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:14:24-debug: asset-db:refresh-all-database (189ms)
2025-8-13 11:14:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 11:14:24-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:14:28-debug: Query all assets info in project
2025-8-13 11:14:28-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 11:14:28-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 11:14:28-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:14:28-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 11:14:28-debug:   Number of all scenes: 1
2025-8-13 11:14:28-debug:   Number of all scripts: 20
2025-8-13 11:14:28-debug:   Number of other assets: 637
2025-8-13 11:14:28-debug: // ---- build task 查询 Asset Bundle ---- (27ms)
2025-8-13 11:14:28-debug: run build task 查询 Asset Bundle success in 27 ms√, progress: 5%
2025-8-13 11:14:28-debug: [Build Memory track]: 查询 Asset Bundle start:162.80MB, end 163.02MB, increase: 224.61KB
2025-8-13 11:14:28-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 11:14:28-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:14:28-debug: // ---- build task 查询 Asset Bundle ---- (16ms)
2025-8-13 11:14:28-debug: run build task 查询 Asset Bundle success in 16 ms√, progress: 10%
2025-8-13 11:14:28-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 11:14:28-debug: [Build Memory track]: 查询 Asset Bundle start:163.06MB, end 163.20MB, increase: 140.77KB
2025-8-13 11:14:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:14:28-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 11:14:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-13 11:14:28-debug: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 12%
2025-8-13 11:14:28-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:163.23MB, end 163.26MB, increase: 27.93KB
2025-8-13 11:14:28-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 11:14:28-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-13 11:14:28-debug: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-13 11:14:28-debug: [Build Memory track]: 填充脚本数据到 settings.json start:163.29MB, end 163.32MB, increase: 31.07KB
2025-8-13 11:14:28-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 11:14:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:14:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (6ms)
2025-8-13 11:14:28-debug: run build task 整理部分构建选项内数据到 settings.json success in 6 ms√, progress: 15%
2025-8-13 11:14:28-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:163.36MB, end 163.50MB, increase: 151.69KB
2025-8-13 11:14:36-debug: Query all assets info in project
2025-8-13 11:14:36-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 11:14:36-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 11:14:36-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:14:36-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 11:14:36-debug:   Number of all scenes: 1
2025-8-13 11:14:36-debug:   Number of all scripts: 20
2025-8-13 11:14:36-debug:   Number of other assets: 637
2025-8-13 11:14:36-debug: // ---- build task 查询 Asset Bundle ---- (45ms)
2025-8-13 11:14:36-debug: run build task 查询 Asset Bundle success in 45 ms√, progress: 5%
2025-8-13 11:14:36-debug: [Build Memory track]: 查询 Asset Bundle start:164.04MB, end 164.19MB, increase: 158.63KB
2025-8-13 11:14:36-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:14:36-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 11:14:36-debug: // ---- build task 查询 Asset Bundle ---- (6ms)
2025-8-13 11:14:36-debug: run build task 查询 Asset Bundle success in 6 ms√, progress: 10%
2025-8-13 11:14:36-debug: [Build Memory track]: 查询 Asset Bundle start:164.23MB, end 164.36MB, increase: 137.09KB
2025-8-13 11:14:36-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 11:14:36-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-13 11:14:36-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:164.40MB, end 164.43MB, increase: 29.55KB
2025-8-13 11:14:36-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 11:14:36-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 11:14:36-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-13 11:14:36-debug: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-13 11:14:36-debug: [Build Memory track]: 填充脚本数据到 settings.json start:164.46MB, end 164.49MB, increase: 30.33KB
2025-8-13 11:14:36-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:14:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (5ms)
2025-8-13 11:14:36-debug: run build task 整理部分构建选项内数据到 settings.json success in 5 ms√, progress: 15%
2025-8-13 11:14:36-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:164.52MB, end 164.66MB, increase: 134.05KB
2025-8-13 11:14:41-debug: Query all assets info in project
2025-8-13 11:14:41-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 11:14:41-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 11:14:41-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:14:41-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 11:14:41-debug:   Number of all scripts: 20
2025-8-13 11:14:41-debug:   Number of other assets: 637
2025-8-13 11:14:41-debug: // ---- build task 查询 Asset Bundle ---- (22ms)
2025-8-13 11:14:41-debug: run build task 查询 Asset Bundle success in 22 ms√, progress: 5%
2025-8-13 11:14:41-debug: [Build Memory track]: 查询 Asset Bundle start:164.94MB, end 165.00MB, increase: 58.57KB
2025-8-13 11:14:41-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 11:14:41-debug:   Number of all scenes: 1
2025-8-13 11:14:41-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:14:41-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-8-13 11:14:41-debug: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-8-13 11:14:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:14:41-debug: [Build Memory track]: 查询 Asset Bundle start:165.03MB, end 165.17MB, increase: 139.67KB
2025-8-13 11:14:41-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 11:14:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 11:14:41-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-13 11:14:41-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 11:14:41-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:165.20MB, end 165.23MB, increase: 29.73KB
2025-8-13 11:14:41-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 11:14:41-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-13 11:14:41-debug: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-13 11:14:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:14:41-debug: [Build Memory track]: 填充脚本数据到 settings.json start:165.27MB, end 165.30MB, increase: 30.65KB
2025-8-13 11:14:41-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 11:14:41-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-8-13 11:14:41-debug: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-8-13 11:14:41-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:165.33MB, end 165.47MB, increase: 143.74KB
2025-8-13 11:14:49-debug: refresh db internal success
2025-8-13 11:14:49-debug: refresh db assets success
2025-8-13 11:14:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:14:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:14:49-debug: asset-db:refresh-all-database (180ms)
2025-8-13 11:14:49-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:14:49-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:15:31-debug: refresh db internal success
2025-8-13 11:15:32-debug: refresh db assets success
2025-8-13 11:15:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:15:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:15:32-debug: asset-db:refresh-all-database (172ms)
2025-8-13 11:15:32-debug: asset-db:worker-effect-data-processing (3ms)
2025-8-13 11:15:32-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 11:16:44-debug: refresh db internal success
2025-8-13 11:16:44-debug: refresh db assets success
2025-8-13 11:16:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:16:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:16:44-debug: asset-db:refresh-all-database (161ms)
2025-8-13 11:16:44-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:16:44-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:16:55-debug: refresh db internal success
2025-8-13 11:16:56-debug: refresh db assets success
2025-8-13 11:16:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:16:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:16:56-debug: asset-db:refresh-all-database (513ms)
2025-8-13 11:16:56-debug: asset-db:worker-effect-data-processing (15ms)
2025-8-13 11:16:56-debug: asset-db-hook-engine-extends-afterRefresh (15ms)
2025-8-13 11:17:13-debug: refresh db internal success
2025-8-13 11:17:13-debug: refresh db assets success
2025-8-13 11:17:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:17:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:17:13-debug: asset-db:refresh-all-database (219ms)
2025-8-13 11:17:13-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 11:17:13-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 11:17:16-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\led-001.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:17:16-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\led-001.prefab...
2025-8-13 11:17:16-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs success
2025-8-13 11:17:17-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:17:22-debug: programming:execute-script (2ms)
2025-8-13 11:17:24-debug: start remove asset E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\led-001.prefab...
2025-8-13 11:17:24-log: 资源数据库已锁定，资源操作(bound _refreshAsset)将会延迟响应，请稍侯
2025-8-13 11:17:24-debug: refresh db internal success
2025-8-13 11:17:24-debug: %cDestroy%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\led-001.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-8-13 11:17:24-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:17:24-debug: refresh db assets success
2025-8-13 11:17:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:17:24-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs success
2025-8-13 11:17:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:17:24-debug: remove asset E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\led-001.prefab success
2025-8-13 11:17:24-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\led-001.prefab...
2025-8-13 11:17:24-debug: asset-db:refresh-all-database (389ms)
2025-8-13 11:17:24-debug: asset-db:worker-effect-data-processing (4ms)
2025-8-13 11:17:24-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-8-13 11:17:44-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\Node.prefab...
2025-8-13 11:17:44-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\Node.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:17:44-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs success
2025-8-13 11:17:44-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:17:53-debug: refresh db internal success
2025-8-13 11:17:53-debug: refresh db assets success
2025-8-13 11:17:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:17:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:17:53-debug: asset-db:refresh-all-database (189ms)
2025-8-13 11:17:53-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:17:53-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:17:54-debug: start move asset from E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\Node.prefab -> E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab...
2025-8-13 11:17:54-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab...
2025-8-13 11:17:54-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:17:54-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs success
2025-8-13 11:17:54-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs...
2025-8-13 11:17:54-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:17:54-debug: move asset from E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\Node.prefab -> E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab success
2025-8-13 11:17:54-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets success
2025-8-13 11:18:54-debug: refresh db internal success
2025-8-13 11:18:54-debug: refresh db assets success
2025-8-13 11:18:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:18:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:18:54-debug: asset-db:refresh-all-database (187ms)
2025-8-13 11:18:54-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:18:54-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 11:18:54-debug: refresh db internal success
2025-8-13 11:18:54-debug: refresh db assets success
2025-8-13 11:18:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:18:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:18:54-debug: asset-db:refresh-all-database (168ms)
2025-8-13 11:18:54-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:18:54-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 11:18:55-debug: refresh db internal success
2025-8-13 11:18:55-debug: refresh db assets success
2025-8-13 11:18:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:18:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:18:55-debug: asset-db:refresh-all-database (157ms)
2025-8-13 11:18:55-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:18:55-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:18:56-debug: refresh db internal success
2025-8-13 11:18:56-debug: refresh db assets success
2025-8-13 11:18:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:18:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:18:56-debug: asset-db:refresh-all-database (167ms)
2025-8-13 11:18:56-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:18:56-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 11:19:04-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl...
2025-8-13 11:19:04-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:19:04-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images success
2025-8-13 11:19:04-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:19:29-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\Group 58 (1).png...
2025-8-13 11:19:29-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\Group 58 (1).png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:19:29-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\Group 58 (1).png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:19:29-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\Group 58 (1).png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:19:29-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl success
2025-8-13 11:19:30-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:19:30-debug: refresh db internal success
2025-8-13 11:19:30-debug: refresh db assets success
2025-8-13 11:19:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:19:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:19:30-debug: asset-db:refresh-all-database (313ms)
2025-8-13 11:19:30-debug: asset-db:worker-effect-data-processing (3ms)
2025-8-13 11:19:30-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-8-13 11:19:43-debug: start move asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\Group 58 (1).png -> E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\pearl.png...
2025-8-13 11:19:43-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\pearl.png...
2025-8-13 11:19:43-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\pearl.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:19:43-debug: %cReImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\pearl.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:19:43-debug: %cReImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\pearl.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:19:43-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl success
2025-8-13 11:19:43-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl...
2025-8-13 11:19:43-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:19:43-debug: move asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\Group 58 (1).png -> E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\pearl.png success
2025-8-13 11:19:43-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images success
2025-8-13 11:19:46-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\Group 58.png...
2025-8-13 11:19:46-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\Group 58.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:19:46-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\Group 58.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:19:46-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\Group 58.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:19:46-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl success
2025-8-13 11:19:46-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:19:46-debug: refresh db internal success
2025-8-13 11:19:46-debug: refresh db assets success
2025-8-13 11:19:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:19:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:19:46-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:19:46-debug: asset-db:refresh-all-database (202ms)
2025-8-13 11:19:46-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:19:51-debug: start move asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\Group 58.png -> E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\light.png...
2025-8-13 11:19:51-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\light.png...
2025-8-13 11:19:51-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\light.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:19:51-debug: %cReImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\light.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:19:51-debug: %cReImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\light.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:19:51-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl...
2025-8-13 11:19:51-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl success
2025-8-13 11:19:51-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:19:51-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images success
2025-8-13 11:19:51-debug: move asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\Group 58.png -> E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\pearl\light.png success
2025-8-13 11:19:57-debug: refresh db internal success
2025-8-13 11:19:57-debug: refresh db assets success
2025-8-13 11:19:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:19:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:19:57-debug: asset-db:refresh-all-database (202ms)
2025-8-13 11:19:57-debug: asset-db:worker-effect-data-processing (5ms)
2025-8-13 11:19:57-debug: asset-db-hook-engine-extends-afterRefresh (5ms)
2025-8-13 11:20:01-debug: refresh db internal success
2025-8-13 11:20:01-debug: refresh db assets success
2025-8-13 11:20:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:20:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:20:01-debug: asset-db:refresh-all-database (169ms)
2025-8-13 11:20:01-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:20:01-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:20:18-debug: refresh db internal success
2025-8-13 11:20:18-debug: refresh db assets success
2025-8-13 11:20:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:20:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:20:18-debug: asset-db:refresh-all-database (176ms)
2025-8-13 11:20:18-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:20:18-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 11:20:35-debug: refresh db internal success
2025-8-13 11:20:35-debug: refresh db assets success
2025-8-13 11:20:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:20:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:20:35-debug: asset-db:refresh-all-database (227ms)
2025-8-13 11:20:35-debug: asset-db:worker-effect-data-processing (3ms)
2025-8-13 11:20:35-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 11:20:42-debug: refresh db internal success
2025-8-13 11:20:42-debug: refresh db assets success
2025-8-13 11:20:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:20:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:20:42-debug: asset-db:refresh-all-database (188ms)
2025-8-13 11:20:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 11:20:42-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:20:53-debug: programming:execute-script (1ms)
2025-8-13 11:20:54-debug: start remove asset E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab...
2025-8-13 11:20:54-log: 资源数据库已锁定，资源操作(bound _refreshAsset)将会延迟响应，请稍侯
2025-8-13 11:20:54-debug: refresh db internal success
2025-8-13 11:20:54-debug: %cDestroy%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-8-13 11:20:54-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:20:54-debug: refresh db assets success
2025-8-13 11:20:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:20:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:20:54-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab...
2025-8-13 11:20:54-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs success
2025-8-13 11:20:54-debug: remove asset E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab success
2025-8-13 11:20:54-debug: asset-db:refresh-all-database (232ms)
2025-8-13 11:20:54-debug: asset-db:worker-effect-data-processing (5ms)
2025-8-13 11:20:54-debug: asset-db-hook-engine-extends-afterRefresh (5ms)
2025-8-13 11:20:56-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab...
2025-8-13 11:20:56-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:20:56-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs success
2025-8-13 11:20:56-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:21:06-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:21:06-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (7ms)
2025-8-13 11:21:08-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:21:08-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (8ms)
2025-8-13 11:21:10-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:21:10-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (10ms)
2025-8-13 11:21:11-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:21:11-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (11ms)
2025-8-13 11:21:14-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:21:14-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (10ms)
2025-8-13 11:21:20-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:21:20-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (9ms)
2025-8-13 11:21:26-debug: refresh db internal success
2025-8-13 11:21:26-debug: refresh db assets success
2025-8-13 11:21:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:21:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:21:26-debug: asset-db:refresh-all-database (179ms)
2025-8-13 11:21:26-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:21:26-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:21:47-debug: refresh db internal success
2025-8-13 11:21:47-debug: refresh db assets success
2025-8-13 11:21:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:21:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:21:47-debug: asset-db:refresh-all-database (170ms)
2025-8-13 11:21:47-debug: asset-db:worker-effect-data-processing (3ms)
2025-8-13 11:21:47-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 11:22:06-debug: refresh db internal success
2025-8-13 11:22:06-debug: refresh db assets success
2025-8-13 11:22:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:22:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:22:06-debug: asset-db:refresh-all-database (184ms)
2025-8-13 11:22:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 11:22:06-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:22:13-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:22:13-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (17ms)
2025-8-13 11:22:13-debug: refresh db internal success
2025-8-13 11:22:13-debug: refresh db assets success
2025-8-13 11:22:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:22:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:22:13-debug: asset-db:refresh-all-database (352ms)
2025-8-13 11:22:13-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 11:22:13-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:22:24-debug: refresh db internal success
2025-8-13 11:22:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:22:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:22:25-debug: refresh db assets success
2025-8-13 11:22:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 11:22:25-debug: asset-db:refresh-all-database (177ms)
2025-8-13 11:22:25-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:22:26-debug: refresh db internal success
2025-8-13 11:22:26-debug: refresh db assets success
2025-8-13 11:22:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:22:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:22:26-debug: asset-db:refresh-all-database (165ms)
2025-8-13 11:22:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 11:22:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 11:22:28-debug: refresh db internal success
2025-8-13 11:22:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:22:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:22:28-debug: refresh db assets success
2025-8-13 11:22:28-debug: asset-db:refresh-all-database (159ms)
2025-8-13 11:22:28-debug: asset-db:worker-effect-data-processing (3ms)
2025-8-13 11:22:28-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 11:22:52-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scenes\ColorLucky.scene
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:22:52-debug: asset-db:reimport-assetbb178f2a-b8ec-40c5-b625-fa77e0041139 (18ms)
2025-8-13 11:22:53-debug: Query all assets info in project
2025-8-13 11:22:53-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 11:22:53-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:22:53-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 11:22:53-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 11:22:53-debug:   Number of all scenes: 1
2025-8-13 11:22:53-debug:   Number of other assets: 644
2025-8-13 11:22:53-debug:   Number of all scripts: 20
2025-8-13 11:22:53-debug: // ---- build task 查询 Asset Bundle ---- (48ms)
2025-8-13 11:22:53-debug: run build task 查询 Asset Bundle success in 48 ms√, progress: 5%
2025-8-13 11:22:53-debug: [Build Memory track]: 查询 Asset Bundle start:167.34MB, end 166.68MB, increase: -678.53KB
2025-8-13 11:22:53-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 11:22:53-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:22:53-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-8-13 11:22:53-debug: [Build Memory track]: 查询 Asset Bundle start:166.71MB, end 166.85MB, increase: 140.66KB
2025-8-13 11:22:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:22:53-debug: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-8-13 11:22:53-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 11:22:53-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-13 11:22:53-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:166.88MB, end 166.91MB, increase: 30.21KB
2025-8-13 11:22:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 11:22:53-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 11:22:53-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 11:22:53-debug: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-13 11:22:53-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-13 11:22:53-debug: [Build Memory track]: 填充脚本数据到 settings.json start:166.95MB, end 166.98MB, increase: 29.52KB
2025-8-13 11:22:53-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 11:22:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:22:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (57ms)
2025-8-13 11:22:53-debug: run build task 整理部分构建选项内数据到 settings.json success in 57 ms√, progress: 15%
2025-8-13 11:22:53-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:167.01MB, end 167.15MB, increase: 138.52KB
2025-8-13 11:23:15-debug: refresh db internal success
2025-8-13 11:23:15-debug: refresh db assets success
2025-8-13 11:23:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:23:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:23:15-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:23:15-debug: asset-db:refresh-all-database (158ms)
2025-8-13 11:23:15-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 11:23:21-debug: refresh db internal success
2025-8-13 11:23:21-debug: refresh db assets success
2025-8-13 11:23:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:23:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:23:21-debug: asset-db:refresh-all-database (158ms)
2025-8-13 11:23:21-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 11:23:21-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:23:30-debug: refresh db internal success
2025-8-13 11:23:30-debug: refresh db assets success
2025-8-13 11:23:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:23:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:23:30-debug: asset-db:refresh-all-database (164ms)
2025-8-13 11:23:30-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:23:30-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:23:52-debug: refresh db internal success
2025-8-13 11:23:52-debug: refresh db assets success
2025-8-13 11:23:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:23:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:23:52-debug: asset-db:refresh-all-database (180ms)
2025-8-13 11:23:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 11:23:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 11:24:07-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\perfabs\Led-001.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:24:07-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\perfabs\Led-001.ts...
2025-8-13 11:24:07-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\perfabs success
2025-8-13 11:24:07-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\perfabs
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:24:08-debug: Query all assets info in project
2025-8-13 11:24:08-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 11:24:08-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 11:24:08-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:24:08-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 11:24:08-debug:   Number of all scripts: 21
2025-8-13 11:24:08-debug:   Number of other assets: 644
2025-8-13 11:24:08-debug:   Number of all scenes: 1
2025-8-13 11:24:08-debug: // ---- build task 查询 Asset Bundle ---- (23ms)
2025-8-13 11:24:08-debug: run build task 查询 Asset Bundle success in 23 ms√, progress: 5%
2025-8-13 11:24:08-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:24:08-debug: [Build Memory track]: 查询 Asset Bundle start:171.69MB, end 172.10MB, increase: 425.40KB
2025-8-13 11:24:08-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 11:24:08-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-8-13 11:24:08-debug: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-8-13 11:24:08-debug: [Build Memory track]: 查询 Asset Bundle start:172.14MB, end 171.59MB, increase: -562.25KB
2025-8-13 11:24:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:24:08-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 11:24:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 11:24:08-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-13 11:24:08-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 11:24:08-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:171.62MB, end 171.65MB, increase: 31.36KB
2025-8-13 11:24:08-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 11:24:08-debug: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-13 11:24:08-debug: [Build Memory track]: 填充脚本数据到 settings.json start:171.69MB, end 171.72MB, increase: 29.07KB
2025-8-13 11:24:08-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-13 11:24:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:24:08-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 11:24:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-13 11:24:08-debug: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-8-13 11:24:08-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:171.75MB, end 171.88MB, increase: 133.35KB
2025-8-13 11:24:22-debug: start move asset from E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\perfabs\Led-001.ts -> E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\perfabs\Pearl.ts...
2025-8-13 11:24:22-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\perfabs\Pearl.ts...
2025-8-13 11:24:22-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\perfabs\Pearl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:24:22-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\perfabs success
2025-8-13 11:24:22-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\perfabs...
2025-8-13 11:24:22-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\perfabs
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:24:22-debug: move asset from E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\perfabs\Led-001.ts -> E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\perfabs\Pearl.ts success
2025-8-13 11:24:22-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\scripts success
2025-8-13 11:24:23-debug: Query all assets info in project
2025-8-13 11:24:23-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 11:24:23-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 11:24:23-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:24:23-debug:   Number of all scenes: 1
2025-8-13 11:24:23-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 11:24:23-debug:   Number of other assets: 644
2025-8-13 11:24:23-debug:   Number of all scripts: 21
2025-8-13 11:24:23-debug: // ---- build task 查询 Asset Bundle ---- (28ms)
2025-8-13 11:24:23-debug: run build task 查询 Asset Bundle success in 28 ms√, progress: 5%
2025-8-13 11:24:23-debug: [Build Memory track]: 查询 Asset Bundle start:172.83MB, end 172.39MB, increase: -458.20KB
2025-8-13 11:24:23-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 11:24:23-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:24:23-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-8-13 11:24:23-debug: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-8-13 11:24:23-debug: [Build Memory track]: 查询 Asset Bundle start:172.42MB, end 172.56MB, increase: 139.96KB
2025-8-13 11:24:23-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 11:24:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:24:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 11:24:23-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-13 11:24:23-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:172.59MB, end 172.62MB, increase: 29.78KB
2025-8-13 11:24:23-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 11:24:23-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 11:24:23-debug: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-13 11:24:23-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 11:24:23-debug: [Build Memory track]: 填充脚本数据到 settings.json start:172.66MB, end 172.68MB, increase: 30.22KB
2025-8-13 11:24:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:24:23-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-13 11:24:23-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-13 11:24:23-debug: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-8-13 11:24:23-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:172.72MB, end 172.85MB, increase: 133.88KB
2025-8-13 11:24:36-debug: refresh db internal success
2025-8-13 11:24:36-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\perfabs\Pearl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:24:36-debug: refresh db assets success
2025-8-13 11:24:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:24:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:24:36-debug: asset-db:refresh-all-database (173ms)
2025-8-13 11:24:36-debug: asset-db:worker-effect-data-processing (3ms)
2025-8-13 11:24:36-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-8-13 11:24:37-debug: Query all assets info in project
2025-8-13 11:24:37-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 11:24:37-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 11:24:37-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:24:37-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 11:24:37-debug:   Number of other assets: 644
2025-8-13 11:24:37-debug:   Number of all scripts: 21
2025-8-13 11:24:37-debug:   Number of all scenes: 1
2025-8-13 11:24:37-debug: // ---- build task 查询 Asset Bundle ---- (30ms)
2025-8-13 11:24:37-debug: run build task 查询 Asset Bundle success in 30 ms√, progress: 5%
2025-8-13 11:24:37-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 11:24:37-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:24:37-debug: [Build Memory track]: 查询 Asset Bundle start:158.30MB, end 158.90MB, increase: 605.84KB
2025-8-13 11:24:37-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-8-13 11:24:37-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 11:24:37-debug: [Build Memory track]: 查询 Asset Bundle start:158.93MB, end 159.10MB, increase: 176.64KB
2025-8-13 11:24:37-debug: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-8-13 11:24:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:24:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-13 11:24:37-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-8-13 11:24:37-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:159.14MB, end 159.17MB, increase: 33.14KB
2025-8-13 11:24:37-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 11:24:37-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 11:24:37-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-13 11:24:37-debug: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-13 11:24:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:24:37-debug: [Build Memory track]: 填充脚本数据到 settings.json start:159.20MB, end 158.51MB, increase: -712.30KB
2025-8-13 11:24:37-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 11:24:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-8-13 11:24:37-debug: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-8-13 11:24:37-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:158.54MB, end 158.68MB, increase: 138.70KB
2025-8-13 11:24:45-debug: refresh db internal success
2025-8-13 11:24:45-debug: refresh db assets success
2025-8-13 11:24:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:24:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:24:45-debug: asset-db:refresh-all-database (172ms)
2025-8-13 11:24:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 11:24:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 11:24:55-debug: refresh db internal success
2025-8-13 11:24:55-debug: refresh db assets success
2025-8-13 11:24:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:24:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:24:55-debug: asset-db:refresh-all-database (174ms)
2025-8-13 11:24:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 11:24:55-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:24:57-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:24:57-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (9ms)
2025-8-13 11:25:06-debug: refresh db internal success
2025-8-13 11:25:06-debug: refresh db assets success
2025-8-13 11:25:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:25:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:25:06-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:25:06-debug: asset-db:refresh-all-database (172ms)
2025-8-13 11:25:06-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:25:08-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:25:08-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (8ms)
2025-8-13 11:25:28-debug: refresh db internal success
2025-8-13 11:25:28-debug: refresh db assets success
2025-8-13 11:25:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:25:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:25:28-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:25:28-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 11:25:28-debug: asset-db:refresh-all-database (197ms)
2025-8-13 11:25:47-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:25:47-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (8ms)
2025-8-13 11:25:50-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:25:50-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (10ms)
2025-8-13 11:26:00-debug: refresh db internal success
2025-8-13 11:26:00-debug: refresh db assets success
2025-8-13 11:26:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:26:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:26:00-debug: asset-db:refresh-all-database (168ms)
2025-8-13 11:26:00-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:26:00-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 11:26:45-debug: refresh db internal success
2025-8-13 11:26:45-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\NumberSpawner.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:26:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:26:45-debug: refresh db assets success
2025-8-13 11:26:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:26:45-debug: asset-db:refresh-all-database (177ms)
2025-8-13 11:26:45-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:26:45-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:26:46-debug: Query all assets info in project
2025-8-13 11:26:46-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 11:26:46-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 11:26:46-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:26:46-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 11:26:46-debug:   Number of all scenes: 1
2025-8-13 11:26:46-debug:   Number of all scripts: 21
2025-8-13 11:26:46-debug:   Number of other assets: 644
2025-8-13 11:26:46-debug: // ---- build task 查询 Asset Bundle ---- (24ms)
2025-8-13 11:26:46-debug: run build task 查询 Asset Bundle success in 24 ms√, progress: 5%
2025-8-13 11:26:46-debug: [Build Memory track]: 查询 Asset Bundle start:167.49MB, end 168.12MB, increase: 647.05KB
2025-8-13 11:26:46-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 11:26:46-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:26:46-debug: // ---- build task 查询 Asset Bundle ---- (31ms)
2025-8-13 11:26:46-debug: run build task 查询 Asset Bundle success in 31 ms√, progress: 10%
2025-8-13 11:26:46-debug: [Build Memory track]: 查询 Asset Bundle start:168.15MB, end 168.29MB, increase: 141.16KB
2025-8-13 11:26:46-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 11:26:46-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:26:46-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 11:26:46-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-13 11:26:46-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:168.32MB, end 168.35MB, increase: 28.86KB
2025-8-13 11:26:46-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 11:26:46-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 11:26:46-debug: // ---- build task 填充脚本数据到 settings.json ---- (12ms)
2025-8-13 11:26:46-debug: run build task 填充脚本数据到 settings.json success in 12 ms√, progress: 13%
2025-8-13 11:26:46-debug: [Build Memory track]: 填充脚本数据到 settings.json start:167.67MB, end 167.70MB, increase: 31.70KB
2025-8-13 11:26:46-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 11:26:46-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:26:46-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (11ms)
2025-8-13 11:26:46-debug: run build task 整理部分构建选项内数据到 settings.json success in 11 ms√, progress: 15%
2025-8-13 11:26:46-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:167.73MB, end 167.88MB, increase: 150.00KB
2025-8-13 11:28:34-debug: refresh db internal success
2025-8-13 11:28:34-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\NumberSpawner.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 11:28:34-debug: refresh db assets success
2025-8-13 11:28:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:28:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:28:34-debug: asset-db:refresh-all-database (184ms)
2025-8-13 11:28:34-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 11:28:34-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 11:28:36-debug: Query all assets info in project
2025-8-13 11:28:36-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 11:28:36-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:28:36-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 11:28:36-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 11:28:36-debug:   Number of all scenes: 1
2025-8-13 11:28:36-debug:   Number of all scripts: 21
2025-8-13 11:28:36-debug:   Number of other assets: 644
2025-8-13 11:28:36-debug: // ---- build task 查询 Asset Bundle ---- (22ms)
2025-8-13 11:28:36-debug: run build task 查询 Asset Bundle success in 22 ms√, progress: 5%
2025-8-13 11:28:36-debug: [Build Memory track]: 查询 Asset Bundle start:169.54MB, end 170.18MB, increase: 655.46KB
2025-8-13 11:28:36-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 11:28:36-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 11:28:36-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-8-13 11:28:36-debug: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-8-13 11:28:36-debug: [Build Memory track]: 查询 Asset Bundle start:170.21MB, end 170.35MB, increase: 138.39KB
2025-8-13 11:28:36-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 11:28:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:28:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-13 11:28:36-debug: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 12%
2025-8-13 11:28:36-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:170.40MB, end 170.43MB, increase: 27.84KB
2025-8-13 11:28:36-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 11:28:36-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 11:28:36-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-13 11:28:36-debug: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-13 11:28:36-debug: [Build Memory track]: 填充脚本数据到 settings.json start:170.46MB, end 170.49MB, increase: 29.86KB
2025-8-13 11:28:36-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 11:28:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 11:28:36-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (9ms)
2025-8-13 11:28:36-debug: run build task 整理部分构建选项内数据到 settings.json success in 9 ms√, progress: 15%
2025-8-13 11:28:36-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:170.53MB, end 169.95MB, increase: -596.66KB
2025-8-13 11:28:36-debug: refresh db internal success
2025-8-13 11:28:36-debug: refresh db assets success
2025-8-13 11:28:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 11:28:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 11:28:36-debug: asset-db:refresh-all-database (153ms)
2025-8-13 11:28:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 11:28:36-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:00:46-debug: refresh db internal success
2025-8-13 13:00:46-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\NumberSpawner.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:00:46-debug: refresh db assets success
2025-8-13 13:00:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:00:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:00:46-debug: asset-db:refresh-all-database (189ms)
2025-8-13 13:00:46-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 13:00:46-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 13:00:47-debug: Query all assets info in project
2025-8-13 13:00:47-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 13:00:47-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:00:47-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 13:00:47-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 13:00:47-debug:   Number of all scenes: 1
2025-8-13 13:00:47-debug:   Number of all scripts: 21
2025-8-13 13:00:47-debug:   Number of other assets: 644
2025-8-13 13:00:47-debug: // ---- build task 查询 Asset Bundle ---- (29ms)
2025-8-13 13:00:47-debug: run build task 查询 Asset Bundle success in 29 ms√, progress: 5%
2025-8-13 13:00:47-debug: [Build Memory track]: 查询 Asset Bundle start:172.05MB, end 172.78MB, increase: 749.43KB
2025-8-13 13:00:47-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 13:00:47-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:00:47-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-8-13 13:00:47-debug: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-8-13 13:00:47-debug: [Build Memory track]: 查询 Asset Bundle start:172.81MB, end 172.95MB, increase: 141.65KB
2025-8-13 13:00:47-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 13:00:47-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:00:47-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 13:00:47-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-13 13:00:47-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:172.99MB, end 173.02MB, increase: 29.94KB
2025-8-13 13:00:47-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 13:00:47-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 13:00:47-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-13 13:00:47-debug: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-13 13:00:47-debug: [Build Memory track]: 填充脚本数据到 settings.json start:173.05MB, end 172.36MB, increase: -709.51KB
2025-8-13 13:00:47-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:00:47-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 13:00:47-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (5ms)
2025-8-13 13:00:47-debug: run build task 整理部分构建选项内数据到 settings.json success in 5 ms√, progress: 15%
2025-8-13 13:00:47-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:172.39MB, end 172.54MB, increase: 149.54KB
2025-8-13 13:01:07-debug: refresh db internal success
2025-8-13 13:01:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:01:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:01:08-debug: refresh db assets success
2025-8-13 13:01:08-debug: asset-db:refresh-all-database (163ms)
2025-8-13 13:01:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:01:08-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:01:44-debug: refresh db internal success
2025-8-13 13:01:44-debug: refresh db assets success
2025-8-13 13:01:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:01:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:01:44-debug: asset-db:refresh-all-database (226ms)
2025-8-13 13:01:44-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:01:44-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:02:20-debug: refresh db internal success
2025-8-13 13:02:20-debug: refresh db assets success
2025-8-13 13:02:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:02:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:02:20-debug: asset-db:refresh-all-database (183ms)
2025-8-13 13:02:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:02:20-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:02:27-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:02:27-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (8ms)
2025-8-13 13:02:29-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:02:29-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (9ms)
2025-8-13 13:02:32-debug: refresh db internal success
2025-8-13 13:02:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:02:32-debug: refresh db assets success
2025-8-13 13:02:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:02:32-debug: asset-db:refresh-all-database (161ms)
2025-8-13 13:02:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:02:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 13:02:39-debug: refresh db internal success
2025-8-13 13:02:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:02:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:02:39-debug: refresh db assets success
2025-8-13 13:02:39-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:02:39-debug: asset-db:refresh-all-database (180ms)
2025-8-13 13:02:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 13:02:40-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:02:40-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (10ms)
2025-8-13 13:02:47-debug: refresh db internal success
2025-8-13 13:02:47-debug: refresh db assets success
2025-8-13 13:02:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:02:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:02:47-debug: asset-db:refresh-all-database (173ms)
2025-8-13 13:02:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:02:47-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:02:47-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scenes\ColorLucky.scene
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:02:47-debug: asset-db:reimport-assetbb178f2a-b8ec-40c5-b625-fa77e0041139 (21ms)
2025-8-13 13:02:48-debug: Query all assets info in project
2025-8-13 13:02:48-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 13:02:48-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 13:02:48-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:02:48-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 13:02:48-debug:   Number of all scenes: 1
2025-8-13 13:02:48-debug:   Number of all scripts: 21
2025-8-13 13:02:48-debug:   Number of other assets: 644
2025-8-13 13:02:48-debug: // ---- build task 查询 Asset Bundle ---- (26ms)
2025-8-13 13:02:48-debug: run build task 查询 Asset Bundle success in 26 ms√, progress: 5%
2025-8-13 13:02:48-debug: [Build Memory track]: 查询 Asset Bundle start:164.69MB, end 166.12MB, increase: 1.43MB
2025-8-13 13:02:48-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 13:02:48-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:02:48-debug: // ---- build task 查询 Asset Bundle ---- (5ms)
2025-8-13 13:02:48-debug: run build task 查询 Asset Bundle success in 5 ms√, progress: 10%
2025-8-13 13:02:48-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:02:48-debug: [Build Memory track]: 查询 Asset Bundle start:166.15MB, end 166.52MB, increase: 375.68KB
2025-8-13 13:02:48-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 13:02:48-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 13:02:48-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-13 13:02:48-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:166.56MB, end 166.59MB, increase: 31.53KB
2025-8-13 13:02:48-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 13:02:48-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 13:02:48-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-13 13:02:48-debug: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-13 13:02:48-debug: [Build Memory track]: 填充脚本数据到 settings.json start:166.62MB, end 166.65MB, increase: 30.51KB
2025-8-13 13:02:48-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 13:02:48-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:02:48-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (6ms)
2025-8-13 13:02:48-debug: run build task 整理部分构建选项内数据到 settings.json success in 6 ms√, progress: 15%
2025-8-13 13:02:48-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:166.68MB, end 166.82MB, increase: 136.66KB
2025-8-13 13:03:22-debug: refresh db internal success
2025-8-13 13:03:22-debug: refresh db assets success
2025-8-13 13:03:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:03:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:03:22-debug: asset-db:refresh-all-database (171ms)
2025-8-13 13:03:22-debug: asset-db:worker-effect-data-processing (10ms)
2025-8-13 13:03:22-debug: asset-db-hook-engine-extends-afterRefresh (11ms)
2025-8-13 13:04:00-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:04:00-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (10ms)
2025-8-13 13:04:05-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:04:05-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (8ms)
2025-8-13 13:04:07-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scenes\ColorLucky.scene
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:04:07-debug: asset-db:reimport-assetbb178f2a-b8ec-40c5-b625-fa77e0041139 (21ms)
2025-8-13 13:04:08-debug: Query all assets info in project
2025-8-13 13:04:08-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 13:04:08-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 13:04:08-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:04:08-debug:   Number of all scenes: 1
2025-8-13 13:04:08-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 13:04:08-debug:   Number of other assets: 644
2025-8-13 13:04:08-debug:   Number of all scripts: 21
2025-8-13 13:04:08-debug: // ---- build task 查询 Asset Bundle ---- (25ms)
2025-8-13 13:04:08-debug: run build task 查询 Asset Bundle success in 25 ms√, progress: 5%
2025-8-13 13:04:08-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:04:08-debug: [Build Memory track]: 查询 Asset Bundle start:168.95MB, end 168.21MB, increase: -765.60KB
2025-8-13 13:04:08-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 13:04:08-debug: // ---- build task 查询 Asset Bundle ---- (6ms)
2025-8-13 13:04:08-debug: run build task 查询 Asset Bundle success in 6 ms√, progress: 10%
2025-8-13 13:04:08-debug: [Build Memory track]: 查询 Asset Bundle start:168.24MB, end 168.38MB, increase: 140.00KB
2025-8-13 13:04:08-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 13:04:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:04:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-13 13:04:08-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 13:04:08-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 13:04:08-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-8-13 13:04:08-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:168.41MB, end 168.44MB, increase: 28.89KB
2025-8-13 13:04:08-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-13 13:04:08-debug: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-13 13:04:08-debug: [Build Memory track]: 填充脚本数据到 settings.json start:168.47MB, end 168.50MB, increase: 29.14KB
2025-8-13 13:04:08-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 13:04:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:04:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (5ms)
2025-8-13 13:04:08-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:168.54MB, end 168.67MB, increase: 135.57KB
2025-8-13 13:04:08-debug: run build task 整理部分构建选项内数据到 settings.json success in 5 ms√, progress: 15%
2025-8-13 13:04:24-debug: refresh db internal success
2025-8-13 13:04:24-debug: refresh db assets success
2025-8-13 13:04:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:04:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:04:24-debug: asset-db:refresh-all-database (185ms)
2025-8-13 13:04:24-debug: asset-db:worker-effect-data-processing (3ms)
2025-8-13 13:04:24-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-8-13 13:05:21-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:05:21-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (8ms)
2025-8-13 13:05:22-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:05:22-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (10ms)
2025-8-13 13:05:24-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scenes\ColorLucky.scene
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:05:24-debug: asset-db:reimport-assetbb178f2a-b8ec-40c5-b625-fa77e0041139 (19ms)
2025-8-13 13:05:25-debug: Query all assets info in project
2025-8-13 13:05:25-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 13:05:25-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 13:05:25-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:05:25-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 13:05:25-debug:   Number of other assets: 644
2025-8-13 13:05:25-debug:   Number of all scenes: 1
2025-8-13 13:05:25-debug:   Number of all scripts: 21
2025-8-13 13:05:25-debug: // ---- build task 查询 Asset Bundle ---- (23ms)
2025-8-13 13:05:25-debug: run build task 查询 Asset Bundle success in 23 ms√, progress: 5%
2025-8-13 13:05:25-debug: [Build Memory track]: 查询 Asset Bundle start:172.09MB, end 171.29MB, increase: -823.68KB
2025-8-13 13:05:25-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 13:05:25-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:05:25-debug: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-8-13 13:05:25-debug: [Build Memory track]: 查询 Asset Bundle start:171.32MB, end 171.46MB, increase: 140.30KB
2025-8-13 13:05:25-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-8-13 13:05:25-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 13:05:25-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:05:25-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-13 13:05:25-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 13:05:25-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-13 13:05:25-debug: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-13 13:05:25-debug: [Build Memory track]: 填充脚本数据到 settings.json start:171.56MB, end 171.58MB, increase: 26.91KB
2025-8-13 13:05:25-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 13:05:25-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 13:05:25-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:171.49MB, end 171.52MB, increase: 28.42KB
2025-8-13 13:05:25-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 13:05:25-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:05:25-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-13 13:05:25-debug: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-8-13 13:05:25-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:171.62MB, end 171.76MB, increase: 143.06KB
2025-8-13 13:09:19-debug: refresh db internal success
2025-8-13 13:09:19-debug: refresh db assets success
2025-8-13 13:09:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:09:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:09:19-debug: asset-db:refresh-all-database (163ms)
2025-8-13 13:09:19-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 13:09:19-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 13:09:26-debug: refresh db internal success
2025-8-13 13:09:26-debug: refresh db assets success
2025-8-13 13:09:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:09:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:09:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:09:26-debug: asset-db:refresh-all-database (168ms)
2025-8-13 13:09:26-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:09:56-debug: refresh db internal success
2025-8-13 13:09:56-debug: refresh db assets success
2025-8-13 13:09:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:09:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:09:56-debug: asset-db:refresh-all-database (238ms)
2025-8-13 13:09:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:09:56-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:10:51-debug: refresh db internal success
2025-8-13 13:10:51-debug: refresh db assets success
2025-8-13 13:10:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:10:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:10:51-debug: asset-db:refresh-all-database (164ms)
2025-8-13 13:10:51-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 13:10:51-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 13:11:06-debug: refresh db internal success
2025-8-13 13:11:06-debug: refresh db assets success
2025-8-13 13:11:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:11:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:11:06-debug: asset-db:refresh-all-database (158ms)
2025-8-13 13:11:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:11:06-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:15:17-debug: refresh db internal success
2025-8-13 13:15:17-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\NumberSpawner.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:15:17-debug: refresh db assets success
2025-8-13 13:15:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:15:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:15:17-debug: asset-db:refresh-all-database (181ms)
2025-8-13 13:15:17-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 13:15:17-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 13:15:18-debug: Query all assets info in project
2025-8-13 13:15:19-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 13:15:19-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 13:15:19-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:15:19-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 13:15:19-debug:   Number of all scenes: 1
2025-8-13 13:15:19-debug:   Number of all scripts: 21
2025-8-13 13:15:19-debug:   Number of other assets: 644
2025-8-13 13:15:19-debug: // ---- build task 查询 Asset Bundle ---- (73ms)
2025-8-13 13:15:19-debug: run build task 查询 Asset Bundle success in 73 ms√, progress: 5%
2025-8-13 13:15:19-debug: [Build Memory track]: 查询 Asset Bundle start:162.82MB, end 163.58MB, increase: 776.60KB
2025-8-13 13:15:19-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:15:19-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 13:15:19-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-8-13 13:15:19-debug: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-8-13 13:15:19-debug: [Build Memory track]: 查询 Asset Bundle start:163.61MB, end 163.75MB, increase: 140.43KB
2025-8-13 13:15:19-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 13:15:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:15:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-13 13:15:19-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-8-13 13:15:19-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:163.79MB, end 163.86MB, increase: 72.38KB
2025-8-13 13:15:19-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 13:15:19-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 13:15:19-debug: // ---- build task 填充脚本数据到 settings.json ---- (26ms)
2025-8-13 13:15:19-debug: run build task 填充脚本数据到 settings.json success in 26 ms√, progress: 13%
2025-8-13 13:15:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:15:19-debug: [Build Memory track]: 填充脚本数据到 settings.json start:163.89MB, end 163.92MB, increase: 33.28KB
2025-8-13 13:15:19-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 13:15:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (5ms)
2025-8-13 13:15:19-debug: run build task 整理部分构建选项内数据到 settings.json success in 5 ms√, progress: 15%
2025-8-13 13:15:19-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:163.96MB, end 164.09MB, increase: 135.11KB
2025-8-13 13:15:56-debug: refresh db internal success
2025-8-13 13:15:56-debug: refresh db assets success
2025-8-13 13:15:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:15:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:15:56-debug: asset-db:refresh-all-database (283ms)
2025-8-13 13:15:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:15:56-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:16:04-debug: programming:execute-script (1ms)
2025-8-13 13:16:06-debug: start remove asset E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\keno.png...
2025-8-13 13:16:06-log: 资源数据库已锁定，资源操作(bound _refreshAsset)将会延迟响应，请稍侯
2025-8-13 13:16:06-debug: refresh db internal success
2025-8-13 13:16:06-debug: %cDestroy%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\keno.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-8-13 13:16:06-debug: %cDestroy%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\keno.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-8-13 13:16:06-debug: %cDestroy%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\keno.png
background: #ffb8b8; color: #000;
color: #000;
2025-8-13 13:16:06-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:16:06-debug: refresh db assets success
2025-8-13 13:16:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:16:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:16:06-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\keno.png...
2025-8-13 13:16:06-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images success
2025-8-13 13:16:06-debug: remove asset E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\keno.png success
2025-8-13 13:16:06-debug: asset-db:refresh-all-database (257ms)
2025-8-13 13:16:06-debug: asset-db:worker-effect-data-processing (3ms)
2025-8-13 13:16:06-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-8-13 13:16:12-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\lucky1_03.png...
2025-8-13 13:16:12-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\lucky1_03.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:16:12-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\lucky1_03.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:16:12-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\lucky1_03.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:16:12-log: 资源数据库已锁定，资源操作(bound autoRefreshAssetLazy)将会延迟响应，请稍侯
2025-8-13 13:16:12-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images success
2025-8-13 13:16:12-debug: refresh db internal success
2025-8-13 13:16:12-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:16:12-debug: refresh db assets success
2025-8-13 13:16:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:16:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:16:12-debug: asset-db:refresh-all-database (343ms)
2025-8-13 13:16:12-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 13:16:12-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 13:16:16-debug: start move asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\lucky1_03.png -> E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\new_logo.png...
2025-8-13 13:16:16-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\new_logo.png...
2025-8-13 13:16:16-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\new_logo.png
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:16:16-debug: %cReImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\new_logo.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:16:16-debug: %cReImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\new_logo.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:16:16-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images success
2025-8-13 13:16:16-debug: start refresh asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images...
2025-8-13 13:16:16-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:16:16-debug: refresh asset E:\Workspace\magic-match\Game\ColorLucky\assets\resources success
2025-8-13 13:16:16-debug: move asset from E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\lucky1_03.png -> E:\Workspace\magic-match\Game\ColorLucky\assets\resources\images\new_logo.png success
2025-8-13 13:16:55-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\spawn.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:16:55-debug: asset-db:reimport-asseta7a8c9a9-f960-42c2-9464-7bad59c3390d (15ms)
2025-8-13 13:17:34-debug: refresh db internal success
2025-8-13 13:17:34-debug: refresh db assets success
2025-8-13 13:17:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:17:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:17:34-debug: asset-db:refresh-all-database (183ms)
2025-8-13 13:17:34-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 13:17:34-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 13:17:36-debug: refresh db internal success
2025-8-13 13:17:36-debug: refresh db assets success
2025-8-13 13:17:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:17:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:17:36-debug: asset-db:refresh-all-database (191ms)
2025-8-13 13:17:36-debug: asset-db:worker-effect-data-processing (3ms)
2025-8-13 13:17:36-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 13:18:05-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\spawn.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:18:05-debug: asset-db:reimport-asseta7a8c9a9-f960-42c2-9464-7bad59c3390d (12ms)
2025-8-13 13:18:12-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\spawn.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:18:12-debug: asset-db:reimport-asseta7a8c9a9-f960-42c2-9464-7bad59c3390d (10ms)
2025-8-13 13:20:50-debug: refresh db internal success
2025-8-13 13:20:50-debug: refresh db assets success
2025-8-13 13:20:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:20:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:20:50-debug: asset-db:refresh-all-database (178ms)
2025-8-13 13:20:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:20:50-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:20:58-debug: refresh db internal success
2025-8-13 13:20:58-debug: refresh db assets success
2025-8-13 13:20:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:20:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:20:58-debug: asset-db:refresh-all-database (175ms)
2025-8-13 13:20:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:20:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 13:21:07-debug: refresh db internal success
2025-8-13 13:21:07-debug: refresh db assets success
2025-8-13 13:21:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:21:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:21:07-debug: asset-db:refresh-all-database (177ms)
2025-8-13 13:21:07-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 13:21:07-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 13:23:16-debug: refresh db internal success
2025-8-13 13:23:16-debug: refresh db assets success
2025-8-13 13:23:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:23:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:23:16-debug: asset-db:refresh-all-database (225ms)
2025-8-13 13:23:16-debug: asset-db:worker-effect-data-processing (3ms)
2025-8-13 13:23:16-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 13:23:17-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\spawn.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:23:17-debug: asset-db:reimport-asseta7a8c9a9-f960-42c2-9464-7bad59c3390d (11ms)
2025-8-13 13:24:44-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\ballsPanel.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:24:44-debug: asset-db:reimport-assetb27443b0-a55c-4e49-868f-1a8b588076ff (9ms)
2025-8-13 13:25:10-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scenes\ColorLucky.scene
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:25:10-debug: asset-db:reimport-assetbb178f2a-b8ec-40c5-b625-fa77e0041139 (19ms)
2025-8-13 13:25:10-debug: Query all assets info in project
2025-8-13 13:25:10-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 13:25:10-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 13:25:10-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:25:10-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 13:25:10-debug:   Number of all scenes: 1
2025-8-13 13:25:10-debug:   Number of other assets: 644
2025-8-13 13:25:10-debug:   Number of all scripts: 21
2025-8-13 13:25:10-debug: // ---- build task 查询 Asset Bundle ---- (30ms)
2025-8-13 13:25:10-debug: run build task 查询 Asset Bundle success in 30 ms√, progress: 5%
2025-8-13 13:25:10-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 13:25:10-debug: [Build Memory track]: 查询 Asset Bundle start:160.64MB, end 160.09MB, increase: -562.63KB
2025-8-13 13:25:10-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:25:10-debug: run build task 查询 Asset Bundle success in 6 ms√, progress: 10%
2025-8-13 13:25:10-debug: [Build Memory track]: 查询 Asset Bundle start:160.13MB, end 160.26MB, increase: 141.09KB
2025-8-13 13:25:10-debug: // ---- build task 查询 Asset Bundle ---- (6ms)
2025-8-13 13:25:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 13:25:10-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-13 13:25:10-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:160.30MB, end 160.33MB, increase: 27.80KB
2025-8-13 13:25:10-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 13:25:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:25:10-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 13:25:10-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 13:25:10-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-13 13:25:10-debug: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-13 13:25:10-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 13:25:10-debug: [Build Memory track]: 填充脚本数据到 settings.json start:160.36MB, end 160.39MB, increase: 30.80KB
2025-8-13 13:25:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:25:10-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (6ms)
2025-8-13 13:25:10-debug: run build task 整理部分构建选项内数据到 settings.json success in 6 ms√, progress: 15%
2025-8-13 13:25:10-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:160.42MB, end 160.56MB, increase: 138.10KB
2025-8-13 13:25:25-debug: refresh db internal success
2025-8-13 13:25:26-debug: refresh db assets success
2025-8-13 13:25:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:25:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:25:26-debug: asset-db:refresh-all-database (223ms)
2025-8-13 13:25:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:25:26-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:25:39-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\spawn.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:25:39-debug: asset-db:reimport-asseta7a8c9a9-f960-42c2-9464-7bad59c3390d (14ms)
2025-8-13 13:25:56-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\spawn.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:25:56-debug: asset-db:reimport-asseta7a8c9a9-f960-42c2-9464-7bad59c3390d (18ms)
2025-8-13 13:25:56-debug: refresh db internal success
2025-8-13 13:25:56-debug: refresh db assets success
2025-8-13 13:25:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:25:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:25:56-debug: asset-db:refresh-all-database (265ms)
2025-8-13 13:25:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:25:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 13:26:06-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:26:06-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (14ms)
2025-8-13 13:26:07-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:26:07-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (13ms)
2025-8-13 13:26:11-debug: refresh db internal success
2025-8-13 13:26:11-debug: refresh db assets success
2025-8-13 13:26:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:26:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:26:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:26:12-debug: asset-db:refresh-all-database (192ms)
2025-8-13 13:26:12-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:26:12-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scenes\ColorLucky.scene
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:26:12-debug: asset-db:reimport-assetbb178f2a-b8ec-40c5-b625-fa77e0041139 (19ms)
2025-8-13 13:26:12-debug: Query all assets info in project
2025-8-13 13:26:12-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 13:26:12-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 13:26:12-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:26:12-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 13:26:12-debug:   Number of all scenes: 1
2025-8-13 13:26:12-debug:   Number of all scripts: 21
2025-8-13 13:26:12-debug:   Number of other assets: 644
2025-8-13 13:26:12-debug: // ---- build task 查询 Asset Bundle ---- (32ms)
2025-8-13 13:26:12-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 13:26:12-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:26:12-debug: run build task 查询 Asset Bundle success in 32 ms√, progress: 5%
2025-8-13 13:26:12-debug: [Build Memory track]: 查询 Asset Bundle start:166.88MB, end 166.64MB, increase: -248.75KB
2025-8-13 13:26:12-debug: run build task 查询 Asset Bundle success in 5 ms√, progress: 10%
2025-8-13 13:26:12-debug: [Build Memory track]: 查询 Asset Bundle start:166.68MB, end 166.81MB, increase: 137.06KB
2025-8-13 13:26:12-debug: // ---- build task 查询 Asset Bundle ---- (5ms)
2025-8-13 13:26:12-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 13:26:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:26:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-13 13:26:12-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:166.84MB, end 166.87MB, increase: 29.71KB
2025-8-13 13:26:12-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 13:26:12-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 13:26:12-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-8-13 13:26:12-debug: [Build Memory track]: 填充脚本数据到 settings.json start:166.91MB, end 166.93MB, increase: 26.95KB
2025-8-13 13:26:12-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 13:26:12-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-13 13:26:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:26:12-debug: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-13 13:26:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (7ms)
2025-8-13 13:26:12-debug: run build task 整理部分构建选项内数据到 settings.json success in 7 ms√, progress: 15%
2025-8-13 13:26:12-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:166.97MB, end 167.10MB, increase: 139.12KB
2025-8-13 13:26:49-debug: Query all assets info in project
2025-8-13 13:26:49-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 13:26:49-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:26:49-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 13:26:49-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 13:26:49-debug:   Number of all scenes: 1
2025-8-13 13:26:49-debug:   Number of all scripts: 21
2025-8-13 13:26:49-debug:   Number of other assets: 644
2025-8-13 13:26:49-debug: // ---- build task 查询 Asset Bundle ---- (22ms)
2025-8-13 13:26:49-debug: run build task 查询 Asset Bundle success in 22 ms√, progress: 5%
2025-8-13 13:26:49-debug: [Build Memory track]: 查询 Asset Bundle start:166.93MB, end 166.85MB, increase: -74.59KB
2025-8-13 13:26:49-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 13:26:49-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:26:49-debug: // ---- build task 查询 Asset Bundle ---- (9ms)
2025-8-13 13:26:49-debug: run build task 查询 Asset Bundle success in 9 ms√, progress: 10%
2025-8-13 13:26:49-debug: [Build Memory track]: 查询 Asset Bundle start:166.89MB, end 167.02MB, increase: 138.13KB
2025-8-13 13:26:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:26:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 13:26:49-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 13:26:49-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-13 13:26:49-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:167.06MB, end 167.08MB, increase: 27.63KB
2025-8-13 13:26:49-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 13:26:49-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 13:26:49-debug: run build task 填充脚本数据到 settings.json success in 4 ms√, progress: 13%
2025-8-13 13:26:49-debug: // ---- build task 填充脚本数据到 settings.json ---- (4ms)
2025-8-13 13:26:49-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 13:26:49-debug: [Build Memory track]: 填充脚本数据到 settings.json start:167.12MB, end 167.15MB, increase: 30.18KB
2025-8-13 13:26:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:26:49-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:167.18MB, end 167.31MB, increase: 135.21KB
2025-8-13 13:26:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (11ms)
2025-8-13 13:26:49-debug: run build task 整理部分构建选项内数据到 settings.json success in 11 ms√, progress: 15%
2025-8-13 13:27:27-debug: refresh db internal success
2025-8-13 13:27:27-debug: refresh db assets success
2025-8-13 13:27:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:27:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:27:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:27:27-debug: asset-db:refresh-all-database (169ms)
2025-8-13 13:27:27-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:27:32-debug: refresh db internal success
2025-8-13 13:27:32-debug: refresh db assets success
2025-8-13 13:27:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:27:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:27:32-debug: asset-db:refresh-all-database (169ms)
2025-8-13 13:27:32-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 13:27:32-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:27:48-debug: refresh db internal success
2025-8-13 13:27:48-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scripts\perfabs\Pearl.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:27:48-debug: refresh db assets success
2025-8-13 13:27:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:27:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:27:48-debug: asset-db:worker-effect-data-processing (7ms)
2025-8-13 13:27:48-debug: asset-db-hook-engine-extends-afterRefresh (8ms)
2025-8-13 13:27:48-debug: asset-db:refresh-all-database (245ms)
2025-8-13 13:27:49-debug: Query all assets info in project
2025-8-13 13:27:49-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 13:27:49-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 13:27:49-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:27:49-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 13:27:49-debug:   Number of all scenes: 1
2025-8-13 13:27:49-debug:   Number of all scripts: 21
2025-8-13 13:27:49-debug:   Number of other assets: 644
2025-8-13 13:27:49-debug: // ---- build task 查询 Asset Bundle ---- (27ms)
2025-8-13 13:27:49-debug: run build task 查询 Asset Bundle success in 27 ms√, progress: 5%
2025-8-13 13:27:49-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:27:49-debug: [Build Memory track]: 查询 Asset Bundle start:170.94MB, end 170.47MB, increase: -477.50KB
2025-8-13 13:27:49-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 13:27:49-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-8-13 13:27:49-debug: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-8-13 13:27:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:27:49-debug: [Build Memory track]: 查询 Asset Bundle start:170.50MB, end 170.64MB, increase: 139.73KB
2025-8-13 13:27:49-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 13:27:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 13:27:49-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-13 13:27:49-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 13:27:49-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:170.67MB, end 170.70MB, increase: 29.63KB
2025-8-13 13:27:49-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 13:27:49-debug: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-8-13 13:27:49-debug: [Build Memory track]: 填充脚本数据到 settings.json start:170.74MB, end 170.76MB, increase: 19.27KB
2025-8-13 13:27:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:27:49-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 13:27:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-13 13:27:49-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:170.79MB, end 170.92MB, increase: 132.43KB
2025-8-13 13:27:49-debug: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-8-13 13:27:51-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\pearl.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:27:51-debug: asset-db:reimport-assetcdbad322-0c26-44c5-8b74-3bdd2210d138 (17ms)
2025-8-13 13:27:51-debug: refresh db internal success
2025-8-13 13:27:51-debug: refresh db assets success
2025-8-13 13:27:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:27:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:27:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:27:51-debug: asset-db:refresh-all-database (219ms)
2025-8-13 13:27:51-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:28:15-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\ballsPanel.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:28:15-debug: asset-db:reimport-assetb27443b0-a55c-4e49-868f-1a8b588076ff (10ms)
2025-8-13 13:28:16-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\ballsPanel.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:28:16-debug: asset-db:reimport-assetb27443b0-a55c-4e49-868f-1a8b588076ff (8ms)
2025-8-13 13:28:52-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\spawn.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:28:52-debug: asset-db:reimport-asseta7a8c9a9-f960-42c2-9464-7bad59c3390d (9ms)
2025-8-13 13:28:55-debug: refresh db internal success
2025-8-13 13:28:55-debug: refresh db assets success
2025-8-13 13:28:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:28:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:28:55-debug: asset-db:refresh-all-database (176ms)
2025-8-13 13:28:55-debug: asset-db:worker-effect-data-processing (3ms)
2025-8-13 13:28:55-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 13:28:55-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\spawn.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:28:55-debug: asset-db:reimport-asseta7a8c9a9-f960-42c2-9464-7bad59c3390d (9ms)
2025-8-13 13:29:12-debug: refresh db internal success
2025-8-13 13:29:12-debug: refresh db assets success
2025-8-13 13:29:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:29:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:29:12-debug: asset-db:refresh-all-database (172ms)
2025-8-13 13:29:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:29:12-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:29:35-debug: refresh db internal success
2025-8-13 13:29:36-debug: refresh db assets success
2025-8-13 13:29:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:29:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:29:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:29:36-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:29:36-debug: asset-db:refresh-all-database (179ms)
2025-8-13 13:29:44-debug: Query all assets info in project
2025-8-13 13:29:44-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 13:29:44-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 13:29:44-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:29:44-debug:   Number of all scripts: 21
2025-8-13 13:29:44-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 13:29:44-debug:   Number of other assets: 644
2025-8-13 13:29:44-debug:   Number of all scenes: 1
2025-8-13 13:29:44-debug: // ---- build task 查询 Asset Bundle ---- (33ms)
2025-8-13 13:29:44-debug: run build task 查询 Asset Bundle success in 33 ms√, progress: 5%
2025-8-13 13:29:44-debug: [Build Memory track]: 查询 Asset Bundle start:161.18MB, end 162.04MB, increase: 872.75KB
2025-8-13 13:29:44-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 13:29:44-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:29:44-debug: // ---- build task 查询 Asset Bundle ---- (10ms)
2025-8-13 13:29:44-debug: run build task 查询 Asset Bundle success in 10 ms√, progress: 10%
2025-8-13 13:29:44-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:29:44-debug: [Build Memory track]: 查询 Asset Bundle start:162.07MB, end 162.21MB, increase: 143.51KB
2025-8-13 13:29:44-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 13:29:44-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-8-13 13:29:44-debug: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 12%
2025-8-13 13:29:44-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:162.25MB, end 162.27MB, increase: 28.88KB
2025-8-13 13:29:44-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 13:29:44-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 13:29:44-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-13 13:29:44-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:29:44-debug: [Build Memory track]: 填充脚本数据到 settings.json start:162.31MB, end 161.62MB, increase: -700.89KB
2025-8-13 13:29:44-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 13:29:44-debug: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-13 13:29:44-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (6ms)
2025-8-13 13:29:44-debug: run build task 整理部分构建选项内数据到 settings.json success in 6 ms√, progress: 15%
2025-8-13 13:29:44-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:161.66MB, end 161.81MB, increase: 154.58KB
2025-8-13 13:29:55-debug: refresh db internal success
2025-8-13 13:29:55-debug: refresh db assets success
2025-8-13 13:29:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:29:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:29:55-debug: asset-db:refresh-all-database (164ms)
2025-8-13 13:29:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:29:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 13:30:04-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scenes\ColorLucky.scene
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:30:04-debug: asset-db:reimport-assetbb178f2a-b8ec-40c5-b625-fa77e0041139 (14ms)
2025-8-13 13:30:07-debug: refresh db internal success
2025-8-13 13:30:07-debug: refresh db assets success
2025-8-13 13:30:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:30:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:30:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:30:07-debug: asset-db:refresh-all-database (165ms)
2025-8-13 13:30:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 13:30:09-debug: Query all assets info in project
2025-8-13 13:30:09-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 13:30:09-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 13:30:09-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:30:09-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 13:30:09-debug:   Number of all scripts: 21
2025-8-13 13:30:09-debug:   Number of other assets: 644
2025-8-13 13:30:09-debug:   Number of all scenes: 1
2025-8-13 13:30:09-debug: // ---- build task 查询 Asset Bundle ---- (29ms)
2025-8-13 13:30:09-debug: run build task 查询 Asset Bundle success in 29 ms√, progress: 5%
2025-8-13 13:30:09-debug: [Build Memory track]: 查询 Asset Bundle start:165.46MB, end 165.89MB, increase: 446.93KB
2025-8-13 13:30:09-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 13:30:09-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:30:09-debug: // ---- build task 查询 Asset Bundle ---- (17ms)
2025-8-13 13:30:09-debug: [Build Memory track]: 查询 Asset Bundle start:165.25MB, end 165.40MB, increase: 153.61KB
2025-8-13 13:30:09-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 13:30:09-debug: run build task 查询 Asset Bundle success in 17 ms√, progress: 10%
2025-8-13 13:30:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:30:09-debug: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-8-13 13:30:09-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:165.43MB, end 165.45MB, increase: 19.11KB
2025-8-13 13:30:09-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 13:30:09-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 13:30:09-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-13 13:30:09-debug: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-13 13:30:09-debug: [Build Memory track]: 填充脚本数据到 settings.json start:165.49MB, end 165.52MB, increase: 28.64KB
2025-8-13 13:30:09-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 13:30:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:30:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (11ms)
2025-8-13 13:30:09-debug: run build task 整理部分构建选项内数据到 settings.json success in 11 ms√, progress: 15%
2025-8-13 13:30:09-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:165.55MB, end 165.68MB, increase: 133.75KB
2025-8-13 13:30:26-debug: refresh db internal success
2025-8-13 13:30:26-debug: refresh db assets success
2025-8-13 13:30:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:30:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:30:26-debug: asset-db:refresh-all-database (162ms)
2025-8-13 13:30:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:30:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 13:30:38-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\spawn.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:30:38-debug: asset-db:reimport-asseta7a8c9a9-f960-42c2-9464-7bad59c3390d (9ms)
2025-8-13 13:30:41-debug: refresh db internal success
2025-8-13 13:30:41-debug: refresh db assets success
2025-8-13 13:30:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:30:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:30:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:30:41-debug: asset-db:refresh-all-database (166ms)
2025-8-13 13:30:41-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:30:42-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\spawn.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:30:42-debug: asset-db:reimport-asseta7a8c9a9-f960-42c2-9464-7bad59c3390d (9ms)
2025-8-13 13:30:45-debug: refresh db internal success
2025-8-13 13:30:45-debug: refresh db assets success
2025-8-13 13:30:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:30:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:30:45-debug: asset-db:refresh-all-database (169ms)
2025-8-13 13:30:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 13:30:50-debug: refresh db internal success
2025-8-13 13:30:51-debug: refresh db assets success
2025-8-13 13:30:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:30:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:30:51-debug: asset-db:refresh-all-database (173ms)
2025-8-13 13:30:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:30:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 13:30:52-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scenes\ColorLucky.scene
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:30:52-debug: asset-db:reimport-assetbb178f2a-b8ec-40c5-b625-fa77e0041139 (18ms)
2025-8-13 13:30:53-debug: Query all assets info in project
2025-8-13 13:30:53-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 13:30:53-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 13:30:53-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:30:53-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 13:30:53-debug:   Number of other assets: 644
2025-8-13 13:30:53-debug:   Number of all scenes: 1
2025-8-13 13:30:53-debug:   Number of all scripts: 21
2025-8-13 13:30:53-debug: // ---- build task 查询 Asset Bundle ---- (27ms)
2025-8-13 13:30:53-debug: run build task 查询 Asset Bundle success in 27 ms√, progress: 5%
2025-8-13 13:30:53-debug: [Build Memory track]: 查询 Asset Bundle start:171.77MB, end 170.76MB, increase: -1028.89KB
2025-8-13 13:30:53-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 13:30:53-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:30:53-debug: // ---- build task 查询 Asset Bundle ---- (5ms)
2025-8-13 13:30:53-debug: run build task 查询 Asset Bundle success in 5 ms√, progress: 10%
2025-8-13 13:30:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:30:53-debug: [Build Memory track]: 查询 Asset Bundle start:170.79MB, end 170.93MB, increase: 140.53KB
2025-8-13 13:30:53-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 13:30:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 13:30:53-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:170.97MB, end 171.00MB, increase: 29.99KB
2025-8-13 13:30:53-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 13:30:53-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 13:30:53-debug: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-13 13:30:53-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 13:30:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:30:53-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-13 13:30:53-debug: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-13 13:30:53-debug: [Build Memory track]: 填充脚本数据到 settings.json start:171.03MB, end 171.06MB, increase: 29.46KB
2025-8-13 13:30:53-debug: run build task 整理部分构建选项内数据到 settings.json success in 5 ms√, progress: 15%
2025-8-13 13:30:53-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:171.09MB, end 171.23MB, increase: 138.47KB
2025-8-13 13:30:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (5ms)
2025-8-13 13:31:00-debug: refresh db internal success
2025-8-13 13:31:00-debug: refresh db assets success
2025-8-13 13:31:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:31:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:31:00-debug: asset-db:refresh-all-database (165ms)
2025-8-13 13:31:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 13:31:00-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 13:31:29-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\spawn.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:31:29-debug: asset-db:reimport-asseta7a8c9a9-f960-42c2-9464-7bad59c3390d (10ms)
2025-8-13 13:31:32-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\spawn.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:31:32-debug: asset-db:reimport-asseta7a8c9a9-f960-42c2-9464-7bad59c3390d (9ms)
2025-8-13 13:31:36-debug: refresh db internal success
2025-8-13 13:31:36-debug: refresh db assets success
2025-8-13 13:31:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:31:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:31:36-debug: asset-db:refresh-all-database (171ms)
2025-8-13 13:31:36-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-13 13:31:36-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-8-13 13:31:36-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\scenes\ColorLucky.scene
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:31:36-debug: asset-db:reimport-assetbb178f2a-b8ec-40c5-b625-fa77e0041139 (95ms)
2025-8-13 13:31:37-debug: Query all assets info in project
2025-8-13 13:31:37-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 13:31:37-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 13:31:37-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:31:37-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 13:31:37-debug:   Number of all scenes: 1
2025-8-13 13:31:37-debug:   Number of all scripts: 21
2025-8-13 13:31:37-debug:   Number of other assets: 644
2025-8-13 13:31:37-debug: // ---- build task 查询 Asset Bundle ---- (28ms)
2025-8-13 13:31:37-debug: run build task 查询 Asset Bundle success in 28 ms√, progress: 5%
2025-8-13 13:31:37-debug: [Build Memory track]: 查询 Asset Bundle start:160.72MB, end 159.47MB, increase: -1278.79KB
2025-8-13 13:31:37-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 13:31:37-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 13:31:37-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-8-13 13:31:37-debug: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-8-13 13:31:37-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 13:31:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:31:37-debug: [Build Memory track]: 查询 Asset Bundle start:159.50MB, end 159.68MB, increase: 186.21KB
2025-8-13 13:31:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-13 13:31:37-debug: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-8-13 13:31:37-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:159.72MB, end 159.75MB, increase: 29.96KB
2025-8-13 13:31:37-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 13:31:37-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 13:31:37-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 13:31:37-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-8-13 13:31:37-debug: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-8-13 13:31:37-debug: [Build Memory track]: 填充脚本数据到 settings.json start:159.78MB, end 159.81MB, increase: 29.39KB
2025-8-13 13:31:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 13:31:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (7ms)
2025-8-13 13:31:37-debug: run build task 整理部分构建选项内数据到 settings.json success in 7 ms√, progress: 15%
2025-8-13 13:31:37-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:159.84MB, end 159.98MB, increase: 137.78KB
2025-8-13 13:32:34-debug: refresh db internal success
2025-8-13 13:32:34-debug: refresh db assets success
2025-8-13 13:32:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 13:32:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 13:32:34-debug: asset-db:refresh-all-database (184ms)
2025-8-13 13:32:34-debug: asset-db:worker-effect-data-processing (4ms)
2025-8-13 13:32:34-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-8-13 13:32:46-debug: %cImport%c: E:\Workspace\magic-match\Game\ColorLucky\assets\perfabs\spawn.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 13:32:46-debug: asset-db:reimport-asseta7a8c9a9-f960-42c2-9464-7bad59c3390d (18ms)
