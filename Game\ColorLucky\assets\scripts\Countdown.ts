import { _decorator, Component, instantiate, Node, Prefab, tween, Vec3 } from 'cc';
import { Led } from './perfabs/Led';
const { ccclass, property } = _decorator;

@ccclass('Countdown')
export class Countdown extends Component {
    @property(Prefab)
    private led: Node = null;

    private currentIndex: number = 0;
    private lightCount: number = 3;
    private intervalTime: number = 1;

    private ledNodes: Led[] = [];
    start() {
        const centerX = 0;
        const centerY = 0;
        const radius = 210;
        const numLights = 20;

        for (let i = numLights; i > 0; i--) {
            const angle = (i / numLights) * Math.PI * 2; // 将圆分成 20 等份
            const x = centerX + radius * Math.cos(angle);
            const y = centerY + radius * Math.sin(angle);

            const ledNode = instantiate(this.led);
            ledNode.setPosition(x, y);
            this.node.addChild(ledNode);


            // 初始化所有灯为暗态
            const ledComponent = ledNode.getComponent(Led);
            ledComponent.dark();
            this.ledNodes.push(ledComponent);
        }

        this.schedule(() => {
            this.tick();
        }, 1, 10);

        // this.startFlowAnimation();

        // this.schedule(() => {


        //     this.node.getComponentsInChildren(Led).forEach(led => {
        //         if (sec % 2 == 0) {
        //             led.light();

        //         } else {
        //             led.dark();
        //         }
        //     });

        //     //这里需要做到一个灯珠循环亮起的功能 每次只往前亮3颗 之前的需要关闭

        //     // this.node.getComponentsInChildren(Led).forEach((led, index) => {
        //     //     if (index < sec % 20) {
        //     //         led.light();
        //     //     } else {
        //     //         led.dark();
        //     //     }
        //     // });

        //     sec++;
        // },1,10);
    }

    public tick() {

        tween(this.node)
            .to(0.5, { scale: this.node.scale })
            .call(() => {
                this.ledNodes.forEach((led, index) => {
                    led.light();
                });
            })
            .to(0.5, { scale: this.node.scale })
            .call(() => {
                this.ledNodes.forEach((led, index) => {
                    led.dark();
                });
            })
            .union()
            .start();
    }

    private startFlowAnimation() {
        this.schedule(() => {
            // 计算当前要亮起的 3 个灯珠索引
            const lightsToShow = [];
            for (let i = 0; i < this.lightCount; i++) {
                lightsToShow.push((this.currentIndex + i) % this.ledNodes.length);
            }

            // 计算上一轮亮起的 3 个灯珠（即当前 - lightCount 到当前 -1）
            const lightsToHide = [];
            for (let i = 0; i < this.lightCount; i++) {
                const index = (this.currentIndex - this.lightCount + i + this.ledNodes.length) % this.ledNodes.length;
                lightsToHide.push(index);
            }

            // 逐个熄灭之前的 3 个灯珠
            lightsToHide.forEach((index, i) => {
                tween(this.ledNodes[index].node)
                    .delay(i * 0.15) // 延迟熄灭时间，与点亮同步
                    .to(0.2, { scale: new Vec3(1, 1, 1) }, { easing: 'backIn' })
                    .call(() => this.ledNodes[index].dark())
                    .start();
            });

            // 逐个点亮新的 3 个灯珠
            lightsToShow.forEach((index, i) => {
                tween(this.ledNodes[index].node)
                    .delay(i * 0.15) // 依次点亮
                    .to(0.2, { scale: new Vec3(1.2, 1.2, 1) }, { easing: 'backOut' })
                    .call(() => this.ledNodes[index].light())
                    .start();
            });

            // 更新当前索引
            this.currentIndex = (this.currentIndex + this.lightCount) % this.ledNodes.length;

        }, this.intervalTime);
    }

    update(deltaTime: number) {

    }
}


