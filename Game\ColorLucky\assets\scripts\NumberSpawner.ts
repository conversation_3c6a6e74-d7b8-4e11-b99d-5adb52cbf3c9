import { _decorator, Component, easing, instantiate, Label, Node, Prefab, tween, UITransform, Vec3, Animation, UIOpacity, Tween } from 'cc';
import { Led } from './perfabs/Led';
import { TweenUtils } from '../framework/utils/TweenUtils';
import { FormatUtils } from '../framework/utils/FormatUtils';
import { NumberUtils } from '../framework/utils/NumberUtils';
import { BallItem } from './perfabs/BallItem';
import { eventManager } from '../framework/event/EventManager';
import { Pearl } from './perfabs/Pearl';
const { ccclass, property } = _decorator;

@ccclass('NumberSpawner')
export class NumberSpawner extends Component {

    @property(Prefab)
    private led: Node = null;

    @property(Label)
    private countdownLabel: Label = null;

    @property(Node)
    private logo: Node = null;

    @property(Node)
    private bar: Node = null;

    @property(Node)
    private animationNode: Node = null;

    private currentIndex: number = 0;
    private lightCount: number = 3;

    private ledNodes: Pearl[] = [];

    private numbers: number[] = [];

    private dataSource: any = null;

    /**
     * 设置数据源并更新UI显示
     * @param data 包含游戏数据的对象，包含command、nextRound、currentRound等字段
     */
    setDataSource(data: any) {
        this.dataSource = data;

        if (data.command == 'ColorLuckyRecentResult') {
            this.node.getChildByPath('/current/next').getComponent(Label).string = 'Next';
            this.node.getChildByPath('/current/sn').getComponent(Label).string = `SN-${this.dataSource.nextRound}`;
        }

        // else {
        //     this.node.getChildByPath('/current/next').getComponent(Label).string = 'Current';
        //     this.node.getChildByPath('/current/sn').getComponent(Label).string = `SN-${this.dataSource.currentRound}`;
        // }
    }

    /**
     * 组件加载时的初始化方法
     * 注册事件监听器并创建圆形排列的LED灯珠
     */
    onLoad() {
        eventManager.on("BallsPanel_Finish", this.onBallsFinish.bind(this));
        eventManager.on("BallsPanel_Start", this.onBallsStart.bind(this));

        const centerX = 0;
        const centerY = 0;
        const radius = 210;
        const numLights = 20;

        const circleNode = this.node.getChildByName('current');

        for (let i = numLights; i > 0; i--) {
            const angle = (i / numLights) * Math.PI * 2 + Math.PI / 2;  // 将圆分成 20 等份 第一个节点在最上面
            const x = centerX + radius * Math.cos(angle);
            const y = centerY + radius * Math.sin(angle);

            const ledNode = instantiate(this.led);

            ledNode.setPosition(x, y);
            // 设置旋转角度，将弧度转换为度数
            const rotationAngle = (angle * 180 / Math.PI) - 90; // 减去90度是为了调整初始方向
            ledNode.setRotationFromEuler(0, 0, rotationAngle);

            circleNode.addChild(ledNode);

            // 初始化所有灯为暗态
            const ledComponent = ledNode.getComponent(Pearl);
            ledComponent.dark();
            this.ledNodes.push(ledComponent);
        }

    }

    /**
     * 球类游戏结束时的回调方法
     * 重置游戏状态，清空数字数组，关闭动画，显示logo
     */
    onBallsFinish() {
        this.numbers = [];
        this.animationNode.active = false;
        this.logo.active = true;
        this.node.getChildByPath('/current/bigBall').active = false;
        this.ledNodes.forEach(ledNode => {
            Tween.stopAllByTarget(ledNode);
            ledNode.dark();
        });

        this.node.getChildByPath('/current/next').getComponent(Label).string = 'Next';
        this.node.getChildByPath('/current/sn').getComponent(Label).string = `SN-${this.dataSource.nextRound}`;
    }

    /**
     * 球类游戏开始时的回调方法
     * 重置LED状态，清空数字数组，隐藏logo和动画
     */
    onBallsStart() {
        this.ledNodes.forEach(ledNode => {
            Tween.stopAllByTarget(ledNode);
            ledNode.dark();
        });
        this.numbers = [];
        this.animationNode.active = false;
        this.logo.active = false;
        this.node.getChildByPath('/current/bigBall').active = false;

        this.node.getChildByPath('/current/next').getComponent(Label).string = 'Current';
        this.node.getChildByPath('/current/sn').getComponent(Label).string = `SN-${this.dataSource.currentRound}`;
    }

    /**
     * 播放弹跳动画
     * 激活动画节点并播放动画
     */
    public playBounceAnimation() {
        this.animationNode.active = true;
        this.animationNode.getComponent(Animation).play();
    }

    /**
     * 按钮动画效果
     * 显示大球的缩放和透明度动画效果
     */
    public btnAni() {
        const opacity = this.node.getChildByPath('/current/bigBall').getComponent(UIOpacity);
        // node.setScale(new Vec3(0.2, 0.2));
        // node.setPosition(new Vec3(0, -170, 0));

        opacity.opacity = 255;
        opacity.node.scale = new Vec3(0.2, 0.2);
        opacity.node.active = true;

        tween(opacity.node)
            .to(0.2, { scale: new Vec3(1.2, 1.2) })
            .to(0.2, { scale: new Vec3(1, 1) })
            .then(tween(opacity).to(2, { opacity: 0 }))
            .union()
            .start();
    }

    /**
     * 设置默认状态
     * 隐藏进度条，显示logo，关闭动画和大球
     */
    public setDefault() {
        this.bar.active = false;
        this.logo.active = true;
        this.animationNode.active = false;
        this.node.getChildByPath('/current/bigBall').active = false;
    }

    /**
     * 显示大数字动画 原为弹跳动画 现切换为多个数字闪动
     * @param number 要显示的数字
     * @param callback 动画完成后的回调函数
     */
    public showBigNumberAnimation2(number: number, callback?: () => void) {
        this.numbers.push(number);
        this.animationNode.active = true;
        // this.animationNode.getComponent(Animation).resume();

        const bigBall = this.node.getChildByPath('/current/bigBall');
        const bigBallOpacity = bigBall.getComponent(UIOpacity);
        bigBall.getComponent(BallItem).setNumber(number);

        Tween.stopAllByTarget(bigBall);
        Tween.stopAllByTarget(bigBallOpacity);

        bigBallOpacity.opacity = 255;
        bigBall.scale = new Vec3(0, 0);
        bigBall.active = true;

        tween(bigBall)
            .delay(0.5)
            .to(0.15, { scale: new Vec3(1.2, 1.2) })
            .to(0.15, { scale: new Vec3(1, 1) })
            .call(() => {
                this.ledNodes[this.numbers.length - 1].light();
                if (callback) {
                    callback();
                }
            })
            .delay(0.7)
            // .then(tween(bigBallOpacity).to(0.2, { opacity: 0 }))
            .union()
            .call(() => {
                if (this.numbers.length == 20) {
                    this.animationNode.active = false;
                } else {
                    bigBallOpacity.opacity = 0;
                    bigBall.active = false;
                }
            })
            .start();


        /*
        if (this.numbers.length == 20) {
            tween(bigBall)
                .to(0.15, { scale: new Vec3(1.2, 1.2) })
                .to(0.2, { scale: new Vec3(1, 1) })
                .call(() => {

                    if (callback) {
                        callback();
                    }

                    this.tick();
                })
                .delay(1)
                .union()
                .call(() => {
                    this.animationNode.active = false;
                    // this.showBarAnimation(number);
                })
                .start();
        } else {
            tween(bigBall)
                .to(0.15, { scale: new Vec3(1.2, 1.2) })
                .to(0.2, { scale: new Vec3(1, 1) })
                .call(() => {
                    this.ledNodes[this.numbers.length - 1].light();
                    if (callback) {
                        callback();
                    }
                })
                .delay(1)
                .then(tween(bigBallOpacity).to(0.2, { opacity: 0 }))
                .union()
                .call(() => {
                    bigBallOpacity.opacity = 0;
                    bigBall.active = false;
                })
                .start();
        }
        */
    }

    /**
     * 显示大数字动画
     * @param number 要显示的数字
     * @param callback 动画完成后的回调函数
     */
    public showBigNumberAnimation(number: number, callback?: () => void) { 
        this.numbers.push(number);
    }



    /**
     * 倒计时功能
     * @param seconds 倒计时秒数
     */
    countdown(seconds: number) {
        //流水灯显示
        if (seconds >= 20) {
            this.startFlowAnimation();
        }

        //  倒计时小于20秒时，开始闪烁led
        if (seconds >= 1 && seconds < 20) {
            this.tick();
        }

        if ((seconds > 0) && (seconds % 5) == 0) {
            TweenUtils.scaleAndRestore(this.logo, 1.15, 0.3, 0);
        }
        if (seconds <= 0) {
            this.ledNodes.forEach(led => { 
                led.getComponent(Pearl).dark();
                led.node.setScale(1, 1, 1);
            });
        }

        const duration = FormatUtils.formatDuration(seconds);
        this.countdownLabel.string = duration;

    }

    /**
     * LED灯珠闪烁效果
     * 让所有LED灯珠同时闪烁一次
     */
    public tick() {
        this.ledNodes.forEach(node => {
            Tween.stopAllByTarget(node);
        });
        const objs = {
            value: 0
        };
        tween(objs)
            .to(0.4, { value: 0 })
            .call(() => {
                this.ledNodes.map(led => led.light());
            })
            .to(0.4, { value: 0 })
            .call(() => {
                this.ledNodes.map(led => led.dark());
            })
            .union()
            .start();
    }

    /**
     * 流水灯动画效果
     * 创建连续的LED灯珠流动效果，每次点亮3个连续的灯珠
     */
    private startFlowAnimation() {
        // 计算当前要亮起的 3 个灯珠索引
        const lightsToShow = [];
        for (let i = 0; i < this.lightCount; i++) {
            lightsToShow.push((this.currentIndex + i) % this.ledNodes.length);
        }

        // 计算上一轮亮起的 3 个灯珠（即当前 - lightCount 到当前 -1）
        const lightsToHide = [];
        for (let i = 0; i < this.lightCount; i++) {
            const index = (this.currentIndex - this.lightCount + i + this.ledNodes.length) % this.ledNodes.length;
            lightsToHide.push(index);
        }

        // 逐个熄灭之前的 3 个灯珠
        lightsToHide.forEach((index, i) => {
            tween(this.ledNodes[index].node)
                .delay(i * 0.2) // 延迟熄灭时间，与点亮同步
                .to(0.2, { scale: new Vec3(1, 1, 1) }, { easing: 'backIn' })
                .call(() => this.ledNodes[index].dark())
                .start();
        });

        // 逐个点亮新的 3 个灯珠
        lightsToShow.forEach((index, i) => {
            tween(this.ledNodes[index].node)
                .delay(i * 0.2) // 依次点亮
                .to(0.2, { scale: new Vec3(1, 1, 1) }, { easing: 'backOut' })
                .call(() => this.ledNodes[index].light())
                .start();
        });

        // 更新当前索引
        this.currentIndex = (this.currentIndex + this.lightCount) % this.ledNodes.length;
    }

    /**
     * 组件启用时的回调方法
     * 当前为空实现，可在此添加启用时的逻辑
     */
    protected onEnable(): void {

    }

    /**
     * 组件禁用时的回调方法
     * 取消所有定时器回调
     */
    protected onDisable(): void {
        this.unscheduleAllCallbacks();
    }


    /**
     * 每帧更新方法
     * @param deltaTime 帧间隔时间
     * 当前为空实现，可在此添加每帧需要执行的逻辑
     */
    update(deltaTime: number) {

    }
}


