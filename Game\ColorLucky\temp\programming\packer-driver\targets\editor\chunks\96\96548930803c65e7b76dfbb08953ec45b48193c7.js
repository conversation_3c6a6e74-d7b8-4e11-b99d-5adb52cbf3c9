System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, instantiate, Label, Node, Prefab, Animation, tween, UITransform, easing, NumberUtils, BallItem, TweenUtils, eventManager, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, BallsPanel;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfNumberUtils(extras) {
    _reporterNs.report("NumberUtils", "../../framework/utils/NumberUtils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBallItem(extras) {
    _reporterNs.report("BallItem", "../perfabs/BallItem", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTweenUtils(extras) {
    _reporterNs.report("TweenUtils", "../../framework/utils/TweenUtils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeventManager(extras) {
    _reporterNs.report("eventManager", "../../framework/event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfNumberSpawner(extras) {
    _reporterNs.report("NumberSpawner", "../NumberSpawner", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
      Label = _cc.Label;
      Node = _cc.Node;
      Prefab = _cc.Prefab;
      Animation = _cc.Animation;
      tween = _cc.tween;
      UITransform = _cc.UITransform;
      easing = _cc.easing;
    }, function (_unresolved_2) {
      NumberUtils = _unresolved_2.NumberUtils;
    }, function (_unresolved_3) {
      BallItem = _unresolved_3.BallItem;
    }, function (_unresolved_4) {
      TweenUtils = _unresolved_4.TweenUtils;
    }, function (_unresolved_5) {
      eventManager = _unresolved_5.eventManager;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c414bl+NYlKEJuW6eItQRNd", "BallsPanel", undefined);

      __checkObsolete__(['_decorator', 'Component', 'instantiate', 'Label', 'Node', 'Prefab', 'Animation', 'tween', 'UITransform', 'easing']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("BallsPanel", BallsPanel = (_dec = ccclass('BallsPanel'), _dec2 = property(Prefab), _dec3 = property(Node), _dec(_class = (_class2 = class BallsPanel extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "hole", _descriptor, this);

          _initializerDefineProperty(this, "bar", _descriptor2, this);

          this.numberSpawner = null;
          this.dataSource = null;
          this.numbers = [];
        }

        setDataSource(data, numberSpawner) {
          console.log("BallsPanel setDataSource", data);
          this.dataSource = data;
          this.numberSpawner = numberSpawner;
        }

        displayBalls() {
          // this.dataSource.currentNumber = NumberUtils.getRandomNumbers(20);
          this.numbers = [];
          const interval = 1.5;
          let numberIdx = 0; // this.numberSpawner.playBounceAnimation();

          this.schedule(() => {
            //小球数字
            const number = this.dataSource.currentNumber[numberIdx];
            this.numbers.push(number);
            console.log("number", number);
            this.numberSpawner.showBigNumberAnimation(number, this.onBigNumberAnimationend.bind(this, number));
            numberIdx++; // this.updateBarAnimation();

            if (numberIdx >= 19) {
              this.scheduleOnce(() => {
                (_crd && eventManager === void 0 ? (_reportPossibleCrUseOfeventManager({
                  error: Error()
                }), eventManager) : eventManager).emit('BallsPanel_Finish'); // TweenUtils.fadeOut(this.node);
              }, 5);
            }
          }, interval, 19, 0);
        }

        onBigNumberAnimationend(number) {
          //小球坑位
          const hole = this.node.getChildByPath('numbers').children[number - 1];
          (_crd && TweenUtils === void 0 ? (_reportPossibleCrUseOfTweenUtils({
            error: Error()
          }), TweenUtils) : TweenUtils).fadeIn(hole.getChildByName('ball'), 0.1);
          hole.getChildByName('ani').active = true;
          const ani = hole.getChildByName('ani').getComponent(Animation);
          ani.play('star');
          this.updateBarAnimation();
        }

        hideAllBalls() {
          this.node.getChildByPath('numbers').children.forEach(node => {
            node.getChildByName('ball').active = false;
            node.getChildByName('ani').active = false;
          });
        }

        updateBarAnimation() {
          const colors = (_crd && NumberUtils === void 0 ? (_reportPossibleCrUseOfNumberUtils({
            error: Error()
          }), NumberUtils) : NumberUtils).getColorCount(this.numbers);
          let gap = 12.5; //当小球个数>10个时所有柱体按照比例增加

          if (this.numbers.length == 20) {
            gap = 250 / Math.max(...colors);
          } else if (this.numbers.length > 10) {
            gap = 250 / (20 - this.numbers.length + Math.max(...colors));
          }

          this.bar.children.forEach((barItem, idx) => {
            const countNode = barItem.getChildByName('text').getComponent(Label);
            countNode.string = colors[idx].toString();
            const targetNode = barItem.getChildByName('value').getComponent(UITransform); // 创建 Tween 动画

            tween(targetNode).to(0.1, {
              height: colors[idx] * gap
            }, {
              easing: easing.linear
            }).start();
          });
        }

        onEnable() {
          (_crd && eventManager === void 0 ? (_reportPossibleCrUseOfeventManager({
            error: Error()
          }), eventManager) : eventManager).emit('BallsPanel_Start');
          this.displayBalls();
        }

        onDisable() {
          this.numbers = [];
          this.unscheduleAllCallbacks();
          this.updateBarAnimation();
          this.hideAllBalls();
        }

        onLoad() {
          console.log("BallsPanel start");
          Array.from({
            length: 80
          }).forEach((_, i) => {
            const hole = instantiate(this.hole);
            hole.getChildByName('text').getComponent(Label).string = (i + 1).toString();
            hole.getChildByName('ball').getComponent(_crd && BallItem === void 0 ? (_reportPossibleCrUseOfBallItem({
              error: Error()
            }), BallItem) : BallItem).setNumber(i + 1);
            this.node.getChildByPath('numbers').addChild(hole);
          });
        }

        update(deltaTime) {}

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "hole", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "bar", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=96548930803c65e7b76dfbb08953ec45b48193c7.js.map