import { Tween, tween, Node, Vec3, UIOpacity, TweenEasing, UITransform, Label, easing } from "cc";

/**
 * 动画工具类
 */
export class TweenUtils {
    /**
     * 放大节点并在指定时间后恢复原始大小
     * @param node 需要执行动画的节点
     * @param scaleTarget 放大目标比例
     * @param duration 动画持续时间（秒）
     */
    public static scaleAndRestore(node: Node, scaleTarget: number = 1.2, duration: number = 0.1, delay: number = 0) {

        //节点不显示 则不执行动画
        if (!node || !node.active) { 
            return;
        }

        // 先停止所有该节点上的动画
        Tween.stopAllByTarget(node);

        // 缓存初始缩放值
        const originScale = node.getScale();

        // 创建放大-缩小动画
        tween(node)
            .delay(delay)
            //放大
            .to(duration / 2, { scale: new Vec3(scaleTarget, scaleTarget, 1) }, { easing: 'linear' })
            //恢复原大小
            .to(duration / 2, { scale: originScale }, { easing: 'linear' })
            .union()
            .start();
    }

    /**
     * 淡入显示节点
     * @param node 节点
     * @param duration 动画时间（秒）
     * @param easing 缓动函数类型
     */
    public static fadeIn(node: Node, duration: number = 0.5, easing: TweenEasing = 'smooth') {

        Tween.stopAllByTarget(node);
        // 获取 UIOpacity 组件，若不存在则添加
        let uiOpacity = node.getComponent(UIOpacity);
        if (!uiOpacity) {
            uiOpacity = node.addComponent(UIOpacity);
        }
        uiOpacity.opacity = 0;
        node.active = true;

        tween(uiOpacity)
            .to(duration, { opacity: 255 }, { easing: easing })
            .start();
    }

    /**
     * 淡出隐藏节点
     * @param node 节点
     * @param duration 动画时间（秒）
     * @param easing 缓动函数类型
     */
    public static fadeOut(node: Node, duration: number = 0.5, easing: TweenEasing = 'smooth', callback?: () => void) {
        Tween.stopAllByTarget(node);
        
        // 如果节点已经隐藏，则直接返回
        if (!node.active) {
            return;
        }
        
        // 获取 UIOpacity 组件，若不存在则添加
        let uiOpacity = node.getComponent(UIOpacity);
        if (!uiOpacity) {
            uiOpacity = node.addComponent(UIOpacity);
        }

        
        tween(uiOpacity)
            .to(duration, { opacity: 0 }, { easing })
            .call(() => {
                node.active = false; // 隐藏后关闭节点
                callback?.();
            })
            .start();
    }

    /**
     * 设置节点高度的 Tween 动画
     * @param node 要操作的节点
     * @param targetHeight 目标高度
     * @param duration 动画持续时间（秒）
     * @param easing 缓动函数类型
     */
    public static tweenHeight(node: Node, targetHeight: number, duration: number = 0.5, easing: TweenEasing = 'linear') {
        // 获取 UITransform 组件
        const uiTransform = node.getComponent(UITransform);
        if (!uiTransform) {
            console.warn(`UITransform component not found on node ${node.name}`);
            return;
        }

        // 创建 Tween 动画
        tween(uiTransform)
            .to(duration / 2, { height: 0 })
            .to(duration, { height: targetHeight }, {
                easing: easing,
                onUpdate: (target: UITransform, ratio: number) => {
                    // 可选：在此处监听动画进度
                }
            })
            .union()
            .start();
    }

    /**
     * 数字从 start 到 end 的 Tween 动画
     * @param node 要更新的 Label 组件
     * @param start 起始数字
     * @param end 目标数字
     * @param duration 动画持续时间（秒）
     * @param isNumber 是否只显示整数（默认 true）
     */
    public static tweenNumber(node: Node, start: number, end: number, duration: number = 1, isNumber: boolean = true) {
        // 获取 UITransform 组件
        const label = node.getComponent(Label);
        if (!label) {
            console.warn(`Label component not found on node ${node.name}`);
            return;
        }
        const progress = { value: start };

        tween(progress)
            .to(duration, { value: end }, {
                onUpdate: (target: { value: number }) => {
                    const displayValue = isNumber ? Math.floor(target.value) : target.value.toFixed(1);
                    label.string = displayValue.toString();
                },
                easing: easing.linear
            })
            .start();
    }


}