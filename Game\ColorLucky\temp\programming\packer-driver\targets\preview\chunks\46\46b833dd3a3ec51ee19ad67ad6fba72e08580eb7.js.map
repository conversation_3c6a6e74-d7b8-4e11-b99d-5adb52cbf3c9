{"version": 3, "sources": ["file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/NumberSpawner.ts"], "names": ["_decorator", "Component", "instantiate", "Label", "Node", "Prefab", "tween", "Vec3", "Animation", "UIOpacity", "Tween", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FormatUtils", "NumberUtils", "BallItem", "eventManager", "Pearl", "ccclass", "property", "NumberSpawner", "currentIndex", "lightCount", "ledNodes", "numbers", "dataSource", "setDataSource", "data", "command", "node", "<PERSON><PERSON><PERSON><PERSON><PERSON>y<PERSON><PERSON>", "getComponent", "string", "nextRound", "onLoad", "on", "onBallsFinish", "bind", "onBallsStart", "centerX", "centerY", "radius", "numLights", "circleNode", "getChildByName", "i", "angle", "Math", "PI", "x", "cos", "y", "sin", "ledNode", "led", "setPosition", "rotationAngle", "setRotationFromEuler", "<PERSON><PERSON><PERSON><PERSON>", "ledComponent", "dark", "push", "animationNode", "active", "logo", "for<PERSON>ach", "stopAllByTarget", "currentRound", "playBounceAnimation", "play", "btnAni", "opacity", "scale", "to", "then", "union", "start", "<PERSON><PERSON><PERSON><PERSON>", "bar", "showBigNumberAnimation2", "number", "callback", "bigBall", "bigBallOpacity", "setNumber", "delay", "call", "length", "light", "showBigNumberAnimation", "randomNumbers", "getRandomNumbers", "num", "idx", "playRandomFlashAnimation", "targetNode", "flashCount", "flashDuration", "onComplete", "currentFlash", "playNextFlash", "randomNumber", "floor", "random", "scheduleOnce", "countdown", "seconds", "startFlowAnimation", "tick", "scaleAndRestore", "setScale", "duration", "formatDuration", "countdown<PERSON><PERSON>l", "objs", "value", "map", "lightsToShow", "lightsToHide", "index", "easing", "onEnable", "onDisable", "unscheduleAllCallbacks", "update", "deltaTime"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAmBC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAoBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;;AAEjHC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,K,iBAAAA,K;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBlB,U;;+BAGjBmB,a,WADZF,OAAO,CAAC,eAAD,C,UAGHC,QAAQ,CAACb,MAAD,C,UAGRa,QAAQ,CAACf,KAAD,C,UAGRe,QAAQ,CAACd,IAAD,C,UAGRc,QAAQ,CAACd,IAAD,C,UAGRc,QAAQ,CAACd,IAAD,C,2BAfb,MACae,aADb,SACmClB,SADnC,CAC6C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAiBjCmB,YAjBiC,GAiBV,CAjBU;AAAA,eAkBjCC,UAlBiC,GAkBZ,CAlBY;AAAA,eAoBjCC,QApBiC,GAoBb,EApBa;AAAA,eAsBjCC,OAtBiC,GAsBb,EAtBa;AAAA,eAwBjCC,UAxBiC,GAwBf,IAxBe;AAAA;;AA0BzC;AACJ;AACA;AACA;AACIC,QAAAA,aAAa,CAACC,IAAD,EAAY;AACrB,eAAKF,UAAL,GAAkBE,IAAlB;;AAEA,cAAIA,IAAI,CAACC,OAAL,IAAgB,wBAApB,EAA8C;AAC1C,iBAAKC,IAAL,CAAUC,cAAV,CAAyB,eAAzB,EAA0CC,YAA1C,CAAuD3B,KAAvD,EAA8D4B,MAA9D,GAAuE,MAAvE;AACA,iBAAKH,IAAL,CAAUC,cAAV,CAAyB,aAAzB,EAAwCC,YAAxC,CAAqD3B,KAArD,EAA4D4B,MAA5D,WAA2E,KAAKP,UAAL,CAAgBQ,SAA3F;AACH,WANoB,CAQrB;AACA;AACA;AACA;;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,MAAM,GAAG;AACL;AAAA;AAAA,4CAAaC,EAAb,CAAgB,mBAAhB,EAAqC,KAAKC,aAAL,CAAmBC,IAAnB,CAAwB,IAAxB,CAArC;AACA;AAAA;AAAA,4CAAaF,EAAb,CAAgB,kBAAhB,EAAoC,KAAKG,YAAL,CAAkBD,IAAlB,CAAuB,IAAvB,CAApC;AAEA,cAAME,OAAO,GAAG,CAAhB;AACA,cAAMC,OAAO,GAAG,CAAhB;AACA,cAAMC,MAAM,GAAG,GAAf;AACA,cAAMC,SAAS,GAAG,EAAlB;AAEA,cAAMC,UAAU,GAAG,KAAKd,IAAL,CAAUe,cAAV,CAAyB,SAAzB,CAAnB;;AAEA,eAAK,IAAIC,CAAC,GAAGH,SAAb,EAAwBG,CAAC,GAAG,CAA5B,EAA+BA,CAAC,EAAhC,EAAoC;AAChC,gBAAMC,KAAK,GAAID,CAAC,GAAGH,SAAL,GAAkBK,IAAI,CAACC,EAAvB,GAA4B,CAA5B,GAAgCD,IAAI,CAACC,EAAL,GAAU,CAAxD,CADgC,CAC4B;;AAC5D,gBAAMC,CAAC,GAAGV,OAAO,GAAGE,MAAM,GAAGM,IAAI,CAACG,GAAL,CAASJ,KAAT,CAA7B;AACA,gBAAMK,CAAC,GAAGX,OAAO,GAAGC,MAAM,GAAGM,IAAI,CAACK,GAAL,CAASN,KAAT,CAA7B;AAEA,gBAAMO,OAAO,GAAGlD,WAAW,CAAC,KAAKmD,GAAN,CAA3B;AAEAD,YAAAA,OAAO,CAACE,WAAR,CAAoBN,CAApB,EAAuBE,CAAvB,EAPgC,CAQhC;;AACA,gBAAMK,aAAa,GAAIV,KAAK,GAAG,GAAR,GAAcC,IAAI,CAACC,EAApB,GAA0B,EAAhD,CATgC,CASoB;;AACpDK,YAAAA,OAAO,CAACI,oBAAR,CAA6B,CAA7B,EAAgC,CAAhC,EAAmCD,aAAnC;AAEAb,YAAAA,UAAU,CAACe,QAAX,CAAoBL,OAApB,EAZgC,CAchC;;AACA,gBAAMM,YAAY,GAAGN,OAAO,CAACtB,YAAR;AAAA;AAAA,+BAArB;AACA4B,YAAAA,YAAY,CAACC,IAAb;AACA,iBAAKrC,QAAL,CAAcsC,IAAd,CAAmBF,YAAnB;AACH;AAEJ;AAED;AACJ;AACA;AACA;;;AACIvB,QAAAA,aAAa,GAAG;AACZ,eAAKZ,OAAL,GAAe,EAAf;AACA,eAAKsC,aAAL,CAAmBC,MAAnB,GAA4B,KAA5B;AACA,eAAKC,IAAL,CAAUD,MAAV,GAAmB,IAAnB;AACA,eAAKlC,IAAL,CAAUC,cAAV,CAAyB,iBAAzB,EAA4CiC,MAA5C,GAAqD,IAArD;AACA,eAAKlC,IAAL,CAAUC,cAAV,CAAyB,oBAAzB,EAA+CiC,MAA/C,GAAwD,KAAxD;AACA,eAAKxC,QAAL,CAAc0C,OAAd,CAAsBZ,OAAO,IAAI;AAC7B1C,YAAAA,KAAK,CAACuD,eAAN,CAAsBb,OAAtB;AACAA,YAAAA,OAAO,CAACO,IAAR;AACH,WAHD;AAKA,eAAK/B,IAAL,CAAUC,cAAV,CAAyB,eAAzB,EAA0CC,YAA1C,CAAuD3B,KAAvD,EAA8D4B,MAA9D,GAAuE,MAAvE;AACA,eAAKH,IAAL,CAAUC,cAAV,CAAyB,aAAzB,EAAwCC,YAAxC,CAAqD3B,KAArD,EAA4D4B,MAA5D,WAA2E,KAAKP,UAAL,CAAgBQ,SAA3F;AACH;AAED;AACJ;AACA;AACA;;;AACIK,QAAAA,YAAY,GAAG;AACX,eAAKf,QAAL,CAAc0C,OAAd,CAAsBZ,OAAO,IAAI;AAC7B1C,YAAAA,KAAK,CAACuD,eAAN,CAAsBb,OAAtB;AACAA,YAAAA,OAAO,CAACO,IAAR;AACH,WAHD;AAIA,eAAKpC,OAAL,GAAe,EAAf;AACA,eAAKsC,aAAL,CAAmBC,MAAnB,GAA4B,KAA5B;AACA,eAAKC,IAAL,CAAUD,MAAV,GAAmB,KAAnB;AACA,eAAKlC,IAAL,CAAUC,cAAV,CAAyB,iBAAzB,EAA4CiC,MAA5C,GAAqD,KAArD;AACA,eAAKlC,IAAL,CAAUC,cAAV,CAAyB,oBAAzB,EAA+CiC,MAA/C,GAAwD,IAAxD;AAEA,eAAKlC,IAAL,CAAUC,cAAV,CAAyB,eAAzB,EAA0CC,YAA1C,CAAuD3B,KAAvD,EAA8D4B,MAA9D,GAAuE,SAAvE;AACA,eAAKH,IAAL,CAAUC,cAAV,CAAyB,aAAzB,EAAwCC,YAAxC,CAAqD3B,KAArD,EAA4D4B,MAA5D,WAA2E,KAAKP,UAAL,CAAgB0C,YAA3F;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,mBAAmB,GAAG;AACzB,eAAKN,aAAL,CAAmBC,MAAnB,GAA4B,IAA5B;AACA,eAAKD,aAAL,CAAmB/B,YAAnB,CAAgCtB,SAAhC,EAA2C4D,IAA3C;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,MAAM,GAAG;AACZ,cAAMC,OAAO,GAAG,KAAK1C,IAAL,CAAUC,cAAV,CAAyB,kBAAzB,EAA6CC,YAA7C,CAA0DrB,SAA1D,CAAhB,CADY,CAEZ;AACA;;AAEA6D,UAAAA,OAAO,CAACA,OAAR,GAAkB,GAAlB;AACAA,UAAAA,OAAO,CAAC1C,IAAR,CAAa2C,KAAb,GAAqB,IAAIhE,IAAJ,CAAS,GAAT,EAAc,GAAd,CAArB;AACA+D,UAAAA,OAAO,CAAC1C,IAAR,CAAakC,MAAb,GAAsB,IAAtB;AAEAxD,UAAAA,KAAK,CAACgE,OAAO,CAAC1C,IAAT,CAAL,CACK4C,EADL,CACQ,GADR,EACa;AAAED,YAAAA,KAAK,EAAE,IAAIhE,IAAJ,CAAS,GAAT,EAAc,GAAd;AAAT,WADb,EAEKiE,EAFL,CAEQ,GAFR,EAEa;AAAED,YAAAA,KAAK,EAAE,IAAIhE,IAAJ,CAAS,CAAT,EAAY,CAAZ;AAAT,WAFb,EAGKkE,IAHL,CAGUnE,KAAK,CAACgE,OAAD,CAAL,CAAeE,EAAf,CAAkB,CAAlB,EAAqB;AAAEF,YAAAA,OAAO,EAAE;AAAX,WAArB,CAHV,EAIKI,KAJL,GAKKC,KALL;AAMH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,UAAU,GAAG;AAChB,eAAKC,GAAL,CAASf,MAAT,GAAkB,KAAlB;AACA,eAAKC,IAAL,CAAUD,MAAV,GAAmB,IAAnB;AACA,eAAKD,aAAL,CAAmBC,MAAnB,GAA4B,KAA5B;AACA,eAAKlC,IAAL,CAAUC,cAAV,CAAyB,kBAAzB,EAA6CiC,MAA7C,GAAsD,KAAtD;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWgB,QAAAA,uBAAuB,CAACC,MAAD,EAAiBC,QAAjB,EAAwC;AAClE,eAAKzD,OAAL,CAAaqC,IAAb,CAAkBmB,MAAlB;AACA,eAAKlB,aAAL,CAAmBC,MAAnB,GAA4B,IAA5B,CAFkE,CAGlE;;AAEA,cAAMmB,OAAO,GAAG,KAAKrD,IAAL,CAAUC,cAAV,CAAyB,kBAAzB,CAAhB;AACA,cAAMqD,cAAc,GAAGD,OAAO,CAACnD,YAAR,CAAqBrB,SAArB,CAAvB;AACAwE,UAAAA,OAAO,CAACnD,YAAR;AAAA;AAAA,oCAA+BqD,SAA/B,CAAyCJ,MAAzC;AAEArE,UAAAA,KAAK,CAACuD,eAAN,CAAsBgB,OAAtB;AACAvE,UAAAA,KAAK,CAACuD,eAAN,CAAsBiB,cAAtB;AAEAA,UAAAA,cAAc,CAACZ,OAAf,GAAyB,GAAzB;AACAW,UAAAA,OAAO,CAACV,KAAR,GAAgB,IAAIhE,IAAJ,CAAS,CAAT,EAAY,CAAZ,CAAhB;AACA0E,UAAAA,OAAO,CAACnB,MAAR,GAAiB,IAAjB;AAEAxD,UAAAA,KAAK,CAAC2E,OAAD,CAAL,CACKG,KADL,CACW,GADX,EAEKZ,EAFL,CAEQ,IAFR,EAEc;AAAED,YAAAA,KAAK,EAAE,IAAIhE,IAAJ,CAAS,GAAT,EAAc,GAAd;AAAT,WAFd,EAGKiE,EAHL,CAGQ,IAHR,EAGc;AAAED,YAAAA,KAAK,EAAE,IAAIhE,IAAJ,CAAS,CAAT,EAAY,CAAZ;AAAT,WAHd,EAIK8E,IAJL,CAIU,MAAM;AACR,iBAAK/D,QAAL,CAAc,KAAKC,OAAL,CAAa+D,MAAb,GAAsB,CAApC,EAAuCC,KAAvC;;AACA,gBAAIP,QAAJ,EAAc;AACVA,cAAAA,QAAQ;AACX;AACJ,WATL,EAUKI,KAVL,CAUW,GAVX,EAWI;AAXJ,WAYKV,KAZL,GAaKW,IAbL,CAaU,MAAM;AACR,gBAAI,KAAK9D,OAAL,CAAa+D,MAAb,IAAuB,EAA3B,EAA+B;AAC3B,mBAAKzB,aAAL,CAAmBC,MAAnB,GAA4B,KAA5B;AACH,aAFD,MAEO;AACHoB,cAAAA,cAAc,CAACZ,OAAf,GAAyB,CAAzB;AACAW,cAAAA,OAAO,CAACnB,MAAR,GAAiB,KAAjB;AACH;AACJ,WApBL,EAqBKa,KArBL;AAwBA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGK;AAED;AACJ;AACA;AACA;AACA;;;AACWa,QAAAA,sBAAsB,CAACT,MAAD,EAAiBC,QAAjB,EAAwC;AACjE,eAAKzD,OAAL,CAAaqC,IAAb,CAAkBmB,MAAlB,EADiE,CAEjE;;AAEA,cAAME,OAAO,GAAG,KAAKrD,IAAL,CAAUC,cAAV,CAAyB,oBAAzB,CAAhB;AACA,cAAMqD,cAAc,GAAGD,OAAO,CAACnD,YAAR,CAAqBrB,SAArB,CAAvB,CALiE,CAMjE;;AACAC,UAAAA,KAAK,CAACuD,eAAN,CAAsBgB,OAAtB;AACAvE,UAAAA,KAAK,CAACuD,eAAN,CAAsBiB,cAAtB,EARiE,CAWjE;;AACA,cAAMO,aAAa,GAAG;AAAA;AAAA,0CAAYC,gBAAZ,CAA6B,CAA7B,CAAtB;AACAD,UAAAA,aAAa,CAAC7B,IAAd,CAAmBmB,MAAnB;AAGAU,UAAAA,aAAa,CAACzB,OAAd,CAAsB,CAAC2B,GAAD,EAAMC,GAAN,KAAc;AAChCtF,YAAAA,KAAK,CAAC2E,OAAD,CAAL,CACKG,KADL,CACW,OAAOQ,GADlB,EAEKP,IAFL,CAEU,MAAM;AACRJ,cAAAA,OAAO,CAACnD,YAAR;AAAA;AAAA,wCAA+BqD,SAA/B,CAAyCQ,GAAzC;;AACA,kBAAGC,GAAG,IAAIH,aAAa,CAACH,MAAd,GAAuB,CAAjC,EAAmC;AAC/B,qBAAKhE,QAAL,CAAc,KAAKC,OAAL,CAAa+D,MAAb,GAAsB,CAApC,EAAuCC,KAAvC;;AACA,oBAAIP,QAAJ,EAAc;AACVA,kBAAAA,QAAQ;AACX;AACJ;AACJ,aAVL,EAWKL,KAXL;AAYH,WAbD,EAhBiE,CAkCjE;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACYkB,QAAAA,wBAAwB,CAACC,UAAD,EAAmBC,UAAnB,EAAuCC,aAAvC,EAA8DC,UAA9D,EAAuF;AACnH,cAAIC,YAAY,GAAG,CAAnB;;AAEA,cAAMC,aAAa,GAAG,MAAM;AACxB,gBAAID,YAAY,IAAIH,UAApB,EAAgC;AAC5B,kBAAIE,UAAJ,EAAgB;AACZA,gBAAAA,UAAU;AACb;;AACD;AACH,aANuB,CAQxB;;;AACA,gBAAMG,YAAY,GAAGtD,IAAI,CAACuD,KAAL,CAAWvD,IAAI,CAACwD,MAAL,KAAgB,EAA3B,IAAiC,CAAtD;AACAR,YAAAA,UAAU,CAAChE,YAAX;AAAA;AAAA,sCAAkCqD,SAAlC,CAA4CiB,YAA5C,EAVwB,CAYxB;;AACA9F,YAAAA,KAAK,CAACwF,UAAD,CAAL,CACKtB,EADL,CACQwB,aAAa,GAAG,GADxB,EAC6B;AAAEzB,cAAAA,KAAK,EAAE,IAAIhE,IAAJ,CAAS,GAAT,EAAc,GAAd,EAAmB,GAAnB;AAAT,aAD7B,EAEKiE,EAFL,CAEQwB,aAAa,GAAG,GAFxB,EAE6B;AAAEzB,cAAAA,KAAK,EAAE,IAAIhE,IAAJ,CAAS,GAAT,EAAc,GAAd,EAAmB,GAAnB;AAAT,aAF7B,EAGKiE,EAHL,CAGQwB,aAAa,GAAG,GAHxB,EAG6B;AAAEzB,cAAAA,KAAK,EAAE,IAAIhE,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf;AAAT,aAH7B,EAIK8E,IAJL,CAIU,MAAM;AACRa,cAAAA,YAAY,GADJ,CAER;;AACA,mBAAKK,YAAL,CAAkB,MAAM;AACpBJ,gBAAAA,aAAa;AAChB,eAFD,EAEG,GAFH;AAGH,aAVL,EAWKxB,KAXL;AAYH,WAzBD,CAHmH,CA8BnH;;;AACAwB,UAAAA,aAAa;AAChB;AAID;AACJ;AACA;AACA;;;AACIK,QAAAA,SAAS,CAACC,OAAD,EAAkB;AACvB;AACA,cAAIA,OAAO,IAAI,EAAf,EAAmB;AACf,iBAAKC,kBAAL;AACH,WAJsB,CAMvB;;;AACA,cAAID,OAAO,IAAI,CAAX,IAAgBA,OAAO,GAAG,EAA9B,EAAkC;AAC9B,iBAAKE,IAAL;AACH;;AAED,cAAKF,OAAO,GAAG,CAAX,IAAkBA,OAAO,GAAG,CAAX,IAAiB,CAAtC,EAAyC;AACrC;AAAA;AAAA,0CAAWG,eAAX,CAA2B,KAAK7C,IAAhC,EAAsC,IAAtC,EAA4C,GAA5C,EAAiD,CAAjD;AACH;;AACD,cAAI0C,OAAO,IAAI,CAAf,EAAkB;AACd,iBAAKnF,QAAL,CAAc0C,OAAd,CAAsBX,GAAG,IAAI;AACzBA,cAAAA,GAAG,CAACvB,YAAJ;AAAA;AAAA,kCAAwB6B,IAAxB;AACAN,cAAAA,GAAG,CAACzB,IAAJ,CAASiF,QAAT,CAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAxB;AACH,aAHD;AAIH;;AAED,cAAMC,QAAQ,GAAG;AAAA;AAAA,0CAAYC,cAAZ,CAA2BN,OAA3B,CAAjB;AACA,eAAKO,cAAL,CAAoBjF,MAApB,GAA6B+E,QAA7B;AAEH;AAED;AACJ;AACA;AACA;;;AACWH,QAAAA,IAAI,GAAG;AACV,eAAKrF,QAAL,CAAc0C,OAAd,CAAsBpC,IAAI,IAAI;AAC1BlB,YAAAA,KAAK,CAACuD,eAAN,CAAsBrC,IAAtB;AACH,WAFD;AAGA,cAAMqF,IAAI,GAAG;AACTC,YAAAA,KAAK,EAAE;AADE,WAAb;AAGA5G,UAAAA,KAAK,CAAC2G,IAAD,CAAL,CACKzC,EADL,CACQ,GADR,EACa;AAAE0C,YAAAA,KAAK,EAAE;AAAT,WADb,EAEK7B,IAFL,CAEU,MAAM;AACR,iBAAK/D,QAAL,CAAc6F,GAAd,CAAkB9D,GAAG,IAAIA,GAAG,CAACkC,KAAJ,EAAzB;AACH,WAJL,EAKKf,EALL,CAKQ,GALR,EAKa;AAAE0C,YAAAA,KAAK,EAAE;AAAT,WALb,EAMK7B,IANL,CAMU,MAAM;AACR,iBAAK/D,QAAL,CAAc6F,GAAd,CAAkB9D,GAAG,IAAIA,GAAG,CAACM,IAAJ,EAAzB;AACH,WARL,EASKe,KATL,GAUKC,KAVL;AAWH;AAED;AACJ;AACA;AACA;;;AACY+B,QAAAA,kBAAkB,GAAG;AACzB;AACA,cAAMU,YAAY,GAAG,EAArB;;AACA,eAAK,IAAIxE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKvB,UAAzB,EAAqCuB,CAAC,EAAtC,EAA0C;AACtCwE,YAAAA,YAAY,CAACxD,IAAb,CAAkB,CAAC,KAAKxC,YAAL,GAAoBwB,CAArB,IAA0B,KAAKtB,QAAL,CAAcgE,MAA1D;AACH,WALwB,CAOzB;;;AACA,cAAM+B,YAAY,GAAG,EAArB;;AACA,eAAK,IAAIzE,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKvB,UAAzB,EAAqCuB,EAAC,EAAtC,EAA0C;AACtC,gBAAM0E,KAAK,GAAG,CAAC,KAAKlG,YAAL,GAAoB,KAAKC,UAAzB,GAAsCuB,EAAtC,GAA0C,KAAKtB,QAAL,CAAcgE,MAAzD,IAAmE,KAAKhE,QAAL,CAAcgE,MAA/F;AACA+B,YAAAA,YAAY,CAACzD,IAAb,CAAkB0D,KAAlB;AACH,WAZwB,CAczB;;;AACAD,UAAAA,YAAY,CAACrD,OAAb,CAAqB,CAACsD,KAAD,EAAQ1E,CAAR,KAAc;AAC/BtC,YAAAA,KAAK,CAAC,KAAKgB,QAAL,CAAcgG,KAAd,EAAqB1F,IAAtB,CAAL,CACKwD,KADL,CACWxC,CAAC,GAAG,GADf,EACoB;AADpB,aAEK4B,EAFL,CAEQ,GAFR,EAEa;AAAED,cAAAA,KAAK,EAAE,IAAIhE,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf;AAAT,aAFb,EAE2C;AAAEgH,cAAAA,MAAM,EAAE;AAAV,aAF3C,EAGKlC,IAHL,CAGU,MAAM,KAAK/D,QAAL,CAAcgG,KAAd,EAAqB3D,IAArB,EAHhB,EAIKgB,KAJL;AAKH,WAND,EAfyB,CAuBzB;;AACAyC,UAAAA,YAAY,CAACpD,OAAb,CAAqB,CAACsD,KAAD,EAAQ1E,CAAR,KAAc;AAC/BtC,YAAAA,KAAK,CAAC,KAAKgB,QAAL,CAAcgG,KAAd,EAAqB1F,IAAtB,CAAL,CACKwD,KADL,CACWxC,CAAC,GAAG,GADf,EACoB;AADpB,aAEK4B,EAFL,CAEQ,GAFR,EAEa;AAAED,cAAAA,KAAK,EAAE,IAAIhE,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf;AAAT,aAFb,EAE2C;AAAEgH,cAAAA,MAAM,EAAE;AAAV,aAF3C,EAGKlC,IAHL,CAGU,MAAM,KAAK/D,QAAL,CAAcgG,KAAd,EAAqB/B,KAArB,EAHhB,EAIKZ,KAJL;AAKH,WAND,EAxByB,CAgCzB;;AACA,eAAKvD,YAAL,GAAoB,CAAC,KAAKA,YAAL,GAAoB,KAAKC,UAA1B,IAAwC,KAAKC,QAAL,CAAcgE,MAA1E;AACH;AAED;AACJ;AACA;AACA;;;AACckC,QAAAA,QAAQ,GAAS,CAE1B;AAED;AACJ;AACA;AACA;;;AACcC,QAAAA,SAAS,GAAS;AACxB,eAAKC,sBAAL;AACH;AAGD;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AA3dwC,O;;;;;iBAGrB,I;;;;;;;iBAGY,I;;;;;;;iBAGX,I;;;;;;;iBAGD,I;;;;;;;iBAGU,I", "sourcesContent": ["import { _decorator, Component, easing, instantiate, Label, Node, Prefab, tween, UITransform, Vec3, Animation, UIOpacity, Tween } from 'cc';\r\nimport { Led } from './perfabs/Led';\r\nimport { TweenUtils } from '../framework/utils/TweenUtils';\r\nimport { FormatUtils } from '../framework/utils/FormatUtils';\r\nimport { NumberUtils } from '../framework/utils/NumberUtils';\r\nimport { BallItem } from './perfabs/BallItem';\r\nimport { eventManager } from '../framework/event/EventManager';\r\nimport { Pearl } from './perfabs/Pearl';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('NumberSpawner')\r\nexport class NumberSpawner extends Component {\r\n\r\n    @property(Prefab)\r\n    private led: Node = null;\r\n\r\n    @property(Label)\r\n    private countdownLabel: Label = null;\r\n\r\n    @property(Node)\r\n    private logo: Node = null;\r\n\r\n    @property(Node)\r\n    private bar: Node = null;\r\n\r\n    @property(Node)\r\n    private animationNode: Node = null;\r\n\r\n    private currentIndex: number = 0;\r\n    private lightCount: number = 3;\r\n\r\n    private ledNodes: Pearl[] = [];\r\n\r\n    private numbers: number[] = [];\r\n\r\n    private dataSource: any = null;\r\n\r\n    /**\r\n     * 设置数据源并更新UI显示\r\n     * @param data 包含游戏数据的对象，包含command、nextRound、currentRound等字段\r\n     */\r\n    setDataSource(data: any) {\r\n        this.dataSource = data;\r\n\r\n        if (data.command == 'ColorLuckyRecentResult') {\r\n            this.node.getChildByPath('/current/next').getComponent(Label).string = 'Next';\r\n            this.node.getChildByPath('/current/sn').getComponent(Label).string = `SN-${this.dataSource.nextRound}`;\r\n        }\r\n\r\n        // else {\r\n        //     this.node.getChildByPath('/current/next').getComponent(Label).string = 'Current';\r\n        //     this.node.getChildByPath('/current/sn').getComponent(Label).string = `SN-${this.dataSource.currentRound}`;\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 组件加载时的初始化方法\r\n     * 注册事件监听器并创建圆形排列的LED灯珠\r\n     */\r\n    onLoad() {\r\n        eventManager.on(\"BallsPanel_Finish\", this.onBallsFinish.bind(this));\r\n        eventManager.on(\"BallsPanel_Start\", this.onBallsStart.bind(this));\r\n\r\n        const centerX = 0;\r\n        const centerY = 0;\r\n        const radius = 230;\r\n        const numLights = 20;\r\n\r\n        const circleNode = this.node.getChildByName('current');\r\n\r\n        for (let i = numLights; i > 0; i--) {\r\n            const angle = (i / numLights) * Math.PI * 2 + Math.PI / 2;  // 将圆分成 20 等份 第一个节点在最上面\r\n            const x = centerX + radius * Math.cos(angle);\r\n            const y = centerY + radius * Math.sin(angle);\r\n\r\n            const ledNode = instantiate(this.led);\r\n\r\n            ledNode.setPosition(x, y);\r\n            // 设置旋转角度，将弧度转换为度数\r\n            const rotationAngle = (angle * 180 / Math.PI) - 90; // 减去90度是为了调整初始方向\r\n            ledNode.setRotationFromEuler(0, 0, rotationAngle);\r\n\r\n            circleNode.addChild(ledNode);\r\n\r\n            // 初始化所有灯为暗态\r\n            const ledComponent = ledNode.getComponent(Pearl);\r\n            ledComponent.dark();\r\n            this.ledNodes.push(ledComponent);\r\n        }\r\n\r\n    }\r\n\r\n    /**\r\n     * 球类游戏结束时的回调方法\r\n     * 重置游戏状态，清空数字数组，关闭动画，显示logo\r\n     */\r\n    onBallsFinish() {\r\n        this.numbers = [];\r\n        this.animationNode.active = false;\r\n        this.logo.active = true;\r\n        this.node.getChildByPath('/current/led_bg').active = true;\r\n        this.node.getChildByPath('/current/largeBall').active = false;\r\n        this.ledNodes.forEach(ledNode => {\r\n            Tween.stopAllByTarget(ledNode);\r\n            ledNode.dark();\r\n        });\r\n\r\n        this.node.getChildByPath('/current/next').getComponent(Label).string = 'Next';\r\n        this.node.getChildByPath('/current/sn').getComponent(Label).string = `SN-${this.dataSource.nextRound}`;\r\n    }\r\n\r\n    /**\r\n     * 球类游戏开始时的回调方法\r\n     * 重置LED状态，清空数字数组，隐藏logo和动画\r\n     */\r\n    onBallsStart() {\r\n        this.ledNodes.forEach(ledNode => {\r\n            Tween.stopAllByTarget(ledNode);\r\n            ledNode.dark();\r\n        });\r\n        this.numbers = [];\r\n        this.animationNode.active = false;\r\n        this.logo.active = false;\r\n        this.node.getChildByPath('/current/led_bg').active = false;\r\n        this.node.getChildByPath('/current/largeBall').active = true;\r\n\r\n        this.node.getChildByPath('/current/next').getComponent(Label).string = 'Current';\r\n        this.node.getChildByPath('/current/sn').getComponent(Label).string = `SN-${this.dataSource.currentRound}`;\r\n    }\r\n\r\n    /**\r\n     * 播放弹跳动画\r\n     * 激活动画节点并播放动画\r\n     */\r\n    public playBounceAnimation() {\r\n        this.animationNode.active = true;\r\n        this.animationNode.getComponent(Animation).play();\r\n    }\r\n\r\n    /**\r\n     * 按钮动画效果\r\n     * 显示大球的缩放和透明度动画效果\r\n     */\r\n    public btnAni() {\r\n        const opacity = this.node.getChildByPath('/current/bigBall').getComponent(UIOpacity);\r\n        // node.setScale(new Vec3(0.2, 0.2));\r\n        // node.setPosition(new Vec3(0, -170, 0));\r\n\r\n        opacity.opacity = 255;\r\n        opacity.node.scale = new Vec3(0.2, 0.2);\r\n        opacity.node.active = true;\r\n\r\n        tween(opacity.node)\r\n            .to(0.2, { scale: new Vec3(1.2, 1.2) })\r\n            .to(0.2, { scale: new Vec3(1, 1) })\r\n            .then(tween(opacity).to(2, { opacity: 0 }))\r\n            .union()\r\n            .start();\r\n    }\r\n\r\n    /**\r\n     * 设置默认状态\r\n     * 隐藏进度条，显示logo，关闭动画和大球\r\n     */\r\n    public setDefault() {\r\n        this.bar.active = false;\r\n        this.logo.active = true;\r\n        this.animationNode.active = false;\r\n        this.node.getChildByPath('/current/bigBall').active = false;\r\n    }\r\n\r\n    /**\r\n     * 显示大数字动画 原为弹跳动画 现切换为多个数字闪动\r\n     * @param number 要显示的数字\r\n     * @param callback 动画完成后的回调函数\r\n     */\r\n    public showBigNumberAnimation2(number: number, callback?: () => void) {\r\n        this.numbers.push(number);\r\n        this.animationNode.active = true;\r\n        // this.animationNode.getComponent(Animation).resume();\r\n\r\n        const bigBall = this.node.getChildByPath('/current/bigBall');\r\n        const bigBallOpacity = bigBall.getComponent(UIOpacity);\r\n        bigBall.getComponent(BallItem).setNumber(number);\r\n\r\n        Tween.stopAllByTarget(bigBall);\r\n        Tween.stopAllByTarget(bigBallOpacity);\r\n\r\n        bigBallOpacity.opacity = 255;\r\n        bigBall.scale = new Vec3(0, 0);\r\n        bigBall.active = true;\r\n\r\n        tween(bigBall)\r\n            .delay(0.5)\r\n            .to(0.15, { scale: new Vec3(1.2, 1.2) })\r\n            .to(0.15, { scale: new Vec3(1, 1) })\r\n            .call(() => {\r\n                this.ledNodes[this.numbers.length - 1].light();\r\n                if (callback) {\r\n                    callback();\r\n                }\r\n            })\r\n            .delay(0.7)\r\n            // .then(tween(bigBallOpacity).to(0.2, { opacity: 0 }))\r\n            .union()\r\n            .call(() => {\r\n                if (this.numbers.length == 20) {\r\n                    this.animationNode.active = false;\r\n                } else {\r\n                    bigBallOpacity.opacity = 0;\r\n                    bigBall.active = false;\r\n                }\r\n            })\r\n            .start();\r\n\r\n\r\n        /*\r\n        if (this.numbers.length == 20) {\r\n            tween(bigBall)\r\n                .to(0.15, { scale: new Vec3(1.2, 1.2) })\r\n                .to(0.2, { scale: new Vec3(1, 1) })\r\n                .call(() => {\r\n\r\n                    if (callback) {\r\n                        callback();\r\n                    }\r\n\r\n                    this.tick();\r\n                })\r\n                .delay(1)\r\n                .union()\r\n                .call(() => {\r\n                    this.animationNode.active = false;\r\n                    // this.showBarAnimation(number);\r\n                })\r\n                .start();\r\n        } else {\r\n            tween(bigBall)\r\n                .to(0.15, { scale: new Vec3(1.2, 1.2) })\r\n                .to(0.2, { scale: new Vec3(1, 1) })\r\n                .call(() => {\r\n                    this.ledNodes[this.numbers.length - 1].light();\r\n                    if (callback) {\r\n                        callback();\r\n                    }\r\n                })\r\n                .delay(1)\r\n                .then(tween(bigBallOpacity).to(0.2, { opacity: 0 }))\r\n                .union()\r\n                .call(() => {\r\n                    bigBallOpacity.opacity = 0;\r\n                    bigBall.active = false;\r\n                })\r\n                .start();\r\n        }\r\n        */\r\n    }\r\n\r\n    /**\r\n     * 显示大数字动画\r\n     * @param number 要显示的数字\r\n     * @param callback 动画完成后的回调函数\r\n     */\r\n    public showBigNumberAnimation(number: number, callback?: () => void) {\r\n        this.numbers.push(number);\r\n        // this.animationNode.active = true;\r\n\r\n        const bigBall = this.node.getChildByPath('/current/largeBall');\r\n        const bigBallOpacity = bigBall.getComponent(UIOpacity);\r\n        // 停止所有正在进行的动画\r\n        Tween.stopAllByTarget(bigBall);\r\n        Tween.stopAllByTarget(bigBallOpacity);\r\n\r\n\r\n        //随机5个数字\r\n        const randomNumbers = NumberUtils.getRandomNumbers(4);\r\n        randomNumbers.push(number);\r\n\r\n\r\n        randomNumbers.forEach((num, idx) => {\r\n            tween(bigBall)\r\n                .delay(0.15 * idx)\r\n                .call(() => {\r\n                    bigBall.getComponent(BallItem).setNumber(num);\r\n                    if(idx == randomNumbers.length - 1){\r\n                        this.ledNodes[this.numbers.length - 1].light();\r\n                        if (callback) {\r\n                            callback();\r\n                        }\r\n                    }\r\n                })\r\n                .start();\r\n        });\r\n\r\n\r\n   \r\n\r\n        // // 随机闪动5次的动画序列\r\n        // this.playRandomFlashAnimation(bigBall, 5, 0.3, () => {\r\n        //     // 闪动完成后显示最终数字\r\n\r\n        //     // 最终数字的缩放动画\r\n        //     tween(bigBall)\r\n        //         .to(0.15, { scale: new Vec3(1.2, 1.2, 1.2) })\r\n        //         .to(0.15, { scale: new Vec3(1, 1, 1) })\r\n        //         .call(() => {\r\n        //             // 点亮对应的LED灯珠\r\n        //             this.ledNodes[this.numbers.length - 1].light();\r\n        //             if (callback) {\r\n        //                 callback();\r\n        //             }\r\n        //         })\r\n        //         .delay(0.7)\r\n        //         .call(() => {\r\n        //             if (this.numbers.length == 20) {\r\n        //                 this.animationNode.active = false;\r\n        //             } else {\r\n        //                 bigBallOpacity.opacity = 0;\r\n        //                 bigBall.active = false;\r\n        //             }\r\n        //         })\r\n        //         .start();\r\n        // });\r\n    }\r\n\r\n    /**\r\n     * 播放随机闪动动画\r\n     * @param targetNode 目标节点\r\n     * @param flashCount 闪动次数\r\n     * @param flashDuration 每次闪动的持续时间\r\n     * @param onComplete 完成回调\r\n     */\r\n    private playRandomFlashAnimation(targetNode: Node, flashCount: number, flashDuration: number, onComplete?: () => void) {\r\n        let currentFlash = 0;\r\n\r\n        const playNextFlash = () => {\r\n            if (currentFlash >= flashCount) {\r\n                if (onComplete) {\r\n                    onComplete();\r\n                }\r\n                return;\r\n            }\r\n\r\n            // 生成1-20之间的随机数字\r\n            const randomNumber = Math.floor(Math.random() * 20) + 1;\r\n            targetNode.getComponent(BallItem).setNumber(randomNumber);\r\n\r\n            // 闪动效果：快速缩放\r\n            tween(targetNode)\r\n                .to(flashDuration * 0.3, { scale: new Vec3(1.1, 1.1, 1.1) })\r\n                .to(flashDuration * 0.4, { scale: new Vec3(0.9, 0.9, 0.9) })\r\n                .to(flashDuration * 0.3, { scale: new Vec3(1, 1, 1) })\r\n                .call(() => {\r\n                    currentFlash++;\r\n                    // 延迟一小段时间后播放下一次闪动\r\n                    this.scheduleOnce(() => {\r\n                        playNextFlash();\r\n                    }, 0.1);\r\n                })\r\n                .start();\r\n        };\r\n\r\n        // 开始第一次闪动\r\n        playNextFlash();\r\n    }\r\n\r\n\r\n\r\n    /**\r\n     * 倒计时功能\r\n     * @param seconds 倒计时秒数\r\n     */\r\n    countdown(seconds: number) {\r\n        //流水灯显示\r\n        if (seconds >= 20) {\r\n            this.startFlowAnimation();\r\n        }\r\n\r\n        //  倒计时小于20秒时，开始闪烁led\r\n        if (seconds >= 1 && seconds < 20) {\r\n            this.tick();\r\n        }\r\n\r\n        if ((seconds > 0) && (seconds % 5) == 0) {\r\n            TweenUtils.scaleAndRestore(this.logo, 1.15, 0.3, 0);\r\n        }\r\n        if (seconds <= 0) {\r\n            this.ledNodes.forEach(led => { \r\n                led.getComponent(Pearl).dark();\r\n                led.node.setScale(1, 1, 1);\r\n            });\r\n        }\r\n\r\n        const duration = FormatUtils.formatDuration(seconds);\r\n        this.countdownLabel.string = duration;\r\n\r\n    }\r\n\r\n    /**\r\n     * LED灯珠闪烁效果\r\n     * 让所有LED灯珠同时闪烁一次\r\n     */\r\n    public tick() {\r\n        this.ledNodes.forEach(node => {\r\n            Tween.stopAllByTarget(node);\r\n        });\r\n        const objs = {\r\n            value: 0\r\n        };\r\n        tween(objs)\r\n            .to(0.4, { value: 0 })\r\n            .call(() => {\r\n                this.ledNodes.map(led => led.light());\r\n            })\r\n            .to(0.4, { value: 0 })\r\n            .call(() => {\r\n                this.ledNodes.map(led => led.dark());\r\n            })\r\n            .union()\r\n            .start();\r\n    }\r\n\r\n    /**\r\n     * 流水灯动画效果\r\n     * 创建连续的LED灯珠流动效果，每次点亮3个连续的灯珠\r\n     */\r\n    private startFlowAnimation() {\r\n        // 计算当前要亮起的 3 个灯珠索引\r\n        const lightsToShow = [];\r\n        for (let i = 0; i < this.lightCount; i++) {\r\n            lightsToShow.push((this.currentIndex + i) % this.ledNodes.length);\r\n        }\r\n\r\n        // 计算上一轮亮起的 3 个灯珠（即当前 - lightCount 到当前 -1）\r\n        const lightsToHide = [];\r\n        for (let i = 0; i < this.lightCount; i++) {\r\n            const index = (this.currentIndex - this.lightCount + i + this.ledNodes.length) % this.ledNodes.length;\r\n            lightsToHide.push(index);\r\n        }\r\n\r\n        // 逐个熄灭之前的 3 个灯珠\r\n        lightsToHide.forEach((index, i) => {\r\n            tween(this.ledNodes[index].node)\r\n                .delay(i * 0.2) // 延迟熄灭时间，与点亮同步\r\n                .to(0.2, { scale: new Vec3(1, 1, 1) }, { easing: 'backIn' })\r\n                .call(() => this.ledNodes[index].dark())\r\n                .start();\r\n        });\r\n\r\n        // 逐个点亮新的 3 个灯珠\r\n        lightsToShow.forEach((index, i) => {\r\n            tween(this.ledNodes[index].node)\r\n                .delay(i * 0.2) // 依次点亮\r\n                .to(0.2, { scale: new Vec3(1, 1, 1) }, { easing: 'backOut' })\r\n                .call(() => this.ledNodes[index].light())\r\n                .start();\r\n        });\r\n\r\n        // 更新当前索引\r\n        this.currentIndex = (this.currentIndex + this.lightCount) % this.ledNodes.length;\r\n    }\r\n\r\n    /**\r\n     * 组件启用时的回调方法\r\n     * 当前为空实现，可在此添加启用时的逻辑\r\n     */\r\n    protected onEnable(): void {\r\n\r\n    }\r\n\r\n    /**\r\n     * 组件禁用时的回调方法\r\n     * 取消所有定时器回调\r\n     */\r\n    protected onDisable(): void {\r\n        this.unscheduleAllCallbacks();\r\n    }\r\n\r\n\r\n    /**\r\n     * 每帧更新方法\r\n     * @param deltaTime 帧间隔时间\r\n     * 当前为空实现，可在此添加每帧需要执行的逻辑\r\n     */\r\n    update(deltaTime: number) {\r\n\r\n    }\r\n}\r\n\r\n\r\n"]}