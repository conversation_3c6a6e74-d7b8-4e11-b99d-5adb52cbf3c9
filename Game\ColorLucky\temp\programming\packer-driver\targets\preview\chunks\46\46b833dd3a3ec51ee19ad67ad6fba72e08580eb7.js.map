{"version": 3, "sources": ["file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/NumberSpawner.ts"], "names": ["_decorator", "Component", "instantiate", "Label", "Node", "Prefab", "tween", "Vec3", "Animation", "UIOpacity", "Tween", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FormatUtils", "BallItem", "eventManager", "Pearl", "ccclass", "property", "NumberSpawner", "currentIndex", "lightCount", "ledNodes", "numbers", "dataSource", "setDataSource", "data", "command", "node", "<PERSON><PERSON><PERSON><PERSON><PERSON>y<PERSON><PERSON>", "getComponent", "string", "nextRound", "onLoad", "on", "onBallsFinish", "bind", "onBallsStart", "centerX", "centerY", "radius", "numLights", "circleNode", "getChildByName", "i", "angle", "Math", "PI", "x", "cos", "y", "sin", "ledNode", "led", "setPosition", "rotationAngle", "setRotationFromEuler", "<PERSON><PERSON><PERSON><PERSON>", "ledComponent", "dark", "push", "animationNode", "active", "logo", "for<PERSON>ach", "stopAllByTarget", "currentRound", "playBounceAnimation", "play", "btnAni", "opacity", "scale", "to", "then", "union", "start", "<PERSON><PERSON><PERSON><PERSON>", "bar", "showBigNumberAnimation", "number", "callback", "bigBall", "bigBallOpacity", "setNumber", "delay", "call", "length", "light", "countdown", "seconds", "startFlowAnimation", "tick", "scaleAndRestore", "setScale", "duration", "formatDuration", "countdown<PERSON><PERSON>l", "objs", "value", "map", "lightsToShow", "lightsToHide", "index", "easing", "onEnable", "onDisable", "unscheduleAllCallbacks", "update", "deltaTime"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAmBC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAoBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;;AAEjHC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,W,iBAAAA,W;;AAEAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,K,iBAAAA,K;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBjB,U;;+BAGjBkB,a,WADZF,OAAO,CAAC,eAAD,C,UAGHC,QAAQ,CAACZ,MAAD,C,UAGRY,QAAQ,CAACd,KAAD,C,UAGRc,QAAQ,CAACb,IAAD,C,UAGRa,QAAQ,CAACb,IAAD,C,UAGRa,QAAQ,CAACb,IAAD,C,2BAfb,MACac,aADb,SACmCjB,SADnC,CAC6C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAiBjCkB,YAjBiC,GAiBV,CAjBU;AAAA,eAkBjCC,UAlBiC,GAkBZ,CAlBY;AAAA,eAoBjCC,QApBiC,GAoBb,EApBa;AAAA,eAsBjCC,OAtBiC,GAsBb,EAtBa;AAAA,eAwBjCC,UAxBiC,GAwBf,IAxBe;AAAA;;AA0BzC;AACJ;AACA;AACA;AACIC,QAAAA,aAAa,CAACC,IAAD,EAAY;AACrB,eAAKF,UAAL,GAAkBE,IAAlB;;AAEA,cAAIA,IAAI,CAACC,OAAL,IAAgB,wBAApB,EAA8C;AAC1C,iBAAKC,IAAL,CAAUC,cAAV,CAAyB,eAAzB,EAA0CC,YAA1C,CAAuD1B,KAAvD,EAA8D2B,MAA9D,GAAuE,MAAvE;AACA,iBAAKH,IAAL,CAAUC,cAAV,CAAyB,aAAzB,EAAwCC,YAAxC,CAAqD1B,KAArD,EAA4D2B,MAA5D,WAA2E,KAAKP,UAAL,CAAgBQ,SAA3F;AACH,WANoB,CAQrB;AACA;AACA;AACA;;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,MAAM,GAAG;AACL;AAAA;AAAA,4CAAaC,EAAb,CAAgB,mBAAhB,EAAqC,KAAKC,aAAL,CAAmBC,IAAnB,CAAwB,IAAxB,CAArC;AACA;AAAA;AAAA,4CAAaF,EAAb,CAAgB,kBAAhB,EAAoC,KAAKG,YAAL,CAAkBD,IAAlB,CAAuB,IAAvB,CAApC;AAEA,cAAME,OAAO,GAAG,CAAhB;AACA,cAAMC,OAAO,GAAG,CAAhB;AACA,cAAMC,MAAM,GAAG,GAAf;AACA,cAAMC,SAAS,GAAG,EAAlB;AAEA,cAAMC,UAAU,GAAG,KAAKd,IAAL,CAAUe,cAAV,CAAyB,SAAzB,CAAnB;;AAEA,eAAK,IAAIC,CAAC,GAAGH,SAAb,EAAwBG,CAAC,GAAG,CAA5B,EAA+BA,CAAC,EAAhC,EAAoC;AAChC,gBAAMC,KAAK,GAAID,CAAC,GAAGH,SAAL,GAAkBK,IAAI,CAACC,EAAvB,GAA4B,CAA5B,GAAgCD,IAAI,CAACC,EAAL,GAAU,CAAxD,CADgC,CAC4B;;AAC5D,gBAAMC,CAAC,GAAGV,OAAO,GAAGE,MAAM,GAAGM,IAAI,CAACG,GAAL,CAASJ,KAAT,CAA7B;AACA,gBAAMK,CAAC,GAAGX,OAAO,GAAGC,MAAM,GAAGM,IAAI,CAACK,GAAL,CAASN,KAAT,CAA7B;AAEA,gBAAMO,OAAO,GAAGjD,WAAW,CAAC,KAAKkD,GAAN,CAA3B;AAEAD,YAAAA,OAAO,CAACE,WAAR,CAAoBN,CAApB,EAAuBE,CAAvB,EAPgC,CAQhC;;AACA,gBAAMK,aAAa,GAAIV,KAAK,GAAG,GAAR,GAAcC,IAAI,CAACC,EAApB,GAA0B,EAAhD,CATgC,CASoB;;AACpDK,YAAAA,OAAO,CAACI,oBAAR,CAA6B,CAA7B,EAAgC,CAAhC,EAAmCD,aAAnC;AAEAb,YAAAA,UAAU,CAACe,QAAX,CAAoBL,OAApB,EAZgC,CAchC;;AACA,gBAAMM,YAAY,GAAGN,OAAO,CAACtB,YAAR;AAAA;AAAA,+BAArB;AACA4B,YAAAA,YAAY,CAACC,IAAb;AACA,iBAAKrC,QAAL,CAAcsC,IAAd,CAAmBF,YAAnB;AACH;AAEJ;AAED;AACJ;AACA;AACA;;;AACIvB,QAAAA,aAAa,GAAG;AACZ,eAAKZ,OAAL,GAAe,EAAf;AACA,eAAKsC,aAAL,CAAmBC,MAAnB,GAA4B,KAA5B;AACA,eAAKC,IAAL,CAAUD,MAAV,GAAmB,IAAnB;AACA,eAAKlC,IAAL,CAAUC,cAAV,CAAyB,kBAAzB,EAA6CiC,MAA7C,GAAsD,KAAtD;AACA,eAAKxC,QAAL,CAAc0C,OAAd,CAAsBZ,OAAO,IAAI;AAC7BzC,YAAAA,KAAK,CAACsD,eAAN,CAAsBb,OAAtB;AACAA,YAAAA,OAAO,CAACO,IAAR;AACH,WAHD;AAKA,eAAK/B,IAAL,CAAUC,cAAV,CAAyB,eAAzB,EAA0CC,YAA1C,CAAuD1B,KAAvD,EAA8D2B,MAA9D,GAAuE,MAAvE;AACA,eAAKH,IAAL,CAAUC,cAAV,CAAyB,aAAzB,EAAwCC,YAAxC,CAAqD1B,KAArD,EAA4D2B,MAA5D,WAA2E,KAAKP,UAAL,CAAgBQ,SAA3F;AACH;AAED;AACJ;AACA;AACA;;;AACIK,QAAAA,YAAY,GAAG;AACX,eAAKf,QAAL,CAAc0C,OAAd,CAAsBZ,OAAO,IAAI;AAC7BzC,YAAAA,KAAK,CAACsD,eAAN,CAAsBb,OAAtB;AACAA,YAAAA,OAAO,CAACO,IAAR;AACH,WAHD;AAIA,eAAKpC,OAAL,GAAe,EAAf;AACA,eAAKsC,aAAL,CAAmBC,MAAnB,GAA4B,KAA5B;AACA,eAAKC,IAAL,CAAUD,MAAV,GAAmB,KAAnB;AACA,eAAKlC,IAAL,CAAUC,cAAV,CAAyB,kBAAzB,EAA6CiC,MAA7C,GAAsD,KAAtD;AAEA,eAAKlC,IAAL,CAAUC,cAAV,CAAyB,eAAzB,EAA0CC,YAA1C,CAAuD1B,KAAvD,EAA8D2B,MAA9D,GAAuE,SAAvE;AACA,eAAKH,IAAL,CAAUC,cAAV,CAAyB,aAAzB,EAAwCC,YAAxC,CAAqD1B,KAArD,EAA4D2B,MAA5D,WAA2E,KAAKP,UAAL,CAAgB0C,YAA3F;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,mBAAmB,GAAG;AACzB,eAAKN,aAAL,CAAmBC,MAAnB,GAA4B,IAA5B;AACA,eAAKD,aAAL,CAAmB/B,YAAnB,CAAgCrB,SAAhC,EAA2C2D,IAA3C;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,MAAM,GAAG;AACZ,cAAMC,OAAO,GAAG,KAAK1C,IAAL,CAAUC,cAAV,CAAyB,kBAAzB,EAA6CC,YAA7C,CAA0DpB,SAA1D,CAAhB,CADY,CAEZ;AACA;;AAEA4D,UAAAA,OAAO,CAACA,OAAR,GAAkB,GAAlB;AACAA,UAAAA,OAAO,CAAC1C,IAAR,CAAa2C,KAAb,GAAqB,IAAI/D,IAAJ,CAAS,GAAT,EAAc,GAAd,CAArB;AACA8D,UAAAA,OAAO,CAAC1C,IAAR,CAAakC,MAAb,GAAsB,IAAtB;AAEAvD,UAAAA,KAAK,CAAC+D,OAAO,CAAC1C,IAAT,CAAL,CACK4C,EADL,CACQ,GADR,EACa;AAAED,YAAAA,KAAK,EAAE,IAAI/D,IAAJ,CAAS,GAAT,EAAc,GAAd;AAAT,WADb,EAEKgE,EAFL,CAEQ,GAFR,EAEa;AAAED,YAAAA,KAAK,EAAE,IAAI/D,IAAJ,CAAS,CAAT,EAAY,CAAZ;AAAT,WAFb,EAGKiE,IAHL,CAGUlE,KAAK,CAAC+D,OAAD,CAAL,CAAeE,EAAf,CAAkB,CAAlB,EAAqB;AAAEF,YAAAA,OAAO,EAAE;AAAX,WAArB,CAHV,EAIKI,KAJL,GAKKC,KALL;AAMH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,UAAU,GAAG;AAChB,eAAKC,GAAL,CAASf,MAAT,GAAkB,KAAlB;AACA,eAAKC,IAAL,CAAUD,MAAV,GAAmB,IAAnB;AACA,eAAKD,aAAL,CAAmBC,MAAnB,GAA4B,KAA5B;AACA,eAAKlC,IAAL,CAAUC,cAAV,CAAyB,kBAAzB,EAA6CiC,MAA7C,GAAsD,KAAtD;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWgB,QAAAA,sBAAsB,CAACC,MAAD,EAAiBC,QAAjB,EAAwC;AACjE,eAAKzD,OAAL,CAAaqC,IAAb,CAAkBmB,MAAlB;AACA,eAAKlB,aAAL,CAAmBC,MAAnB,GAA4B,IAA5B,CAFiE,CAGjE;;AAEA,cAAMmB,OAAO,GAAG,KAAKrD,IAAL,CAAUC,cAAV,CAAyB,kBAAzB,CAAhB;AACA,cAAMqD,cAAc,GAAGD,OAAO,CAACnD,YAAR,CAAqBpB,SAArB,CAAvB;AACAuE,UAAAA,OAAO,CAACnD,YAAR;AAAA;AAAA,oCAA+BqD,SAA/B,CAAyCJ,MAAzC;AAEApE,UAAAA,KAAK,CAACsD,eAAN,CAAsBgB,OAAtB;AACAtE,UAAAA,KAAK,CAACsD,eAAN,CAAsBiB,cAAtB;AAEAA,UAAAA,cAAc,CAACZ,OAAf,GAAyB,GAAzB;AACAW,UAAAA,OAAO,CAACV,KAAR,GAAgB,IAAI/D,IAAJ,CAAS,CAAT,EAAY,CAAZ,CAAhB;AACAyE,UAAAA,OAAO,CAACnB,MAAR,GAAiB,IAAjB;AAEAvD,UAAAA,KAAK,CAAC0E,OAAD,CAAL,CACKG,KADL,CACW,GADX,EAEKZ,EAFL,CAEQ,IAFR,EAEc;AAAED,YAAAA,KAAK,EAAE,IAAI/D,IAAJ,CAAS,GAAT,EAAc,GAAd;AAAT,WAFd,EAGKgE,EAHL,CAGQ,IAHR,EAGc;AAAED,YAAAA,KAAK,EAAE,IAAI/D,IAAJ,CAAS,CAAT,EAAY,CAAZ;AAAT,WAHd,EAIK6E,IAJL,CAIU,MAAM;AACR,iBAAK/D,QAAL,CAAc,KAAKC,OAAL,CAAa+D,MAAb,GAAsB,CAApC,EAAuCC,KAAvC;;AACA,gBAAIP,QAAJ,EAAc;AACVA,cAAAA,QAAQ;AACX;AACJ,WATL,EAUKI,KAVL,CAUW,GAVX,EAWI;AAXJ,WAYKV,KAZL,GAaKW,IAbL,CAaU,MAAM;AACR,gBAAI,KAAK9D,OAAL,CAAa+D,MAAb,IAAuB,EAA3B,EAA+B;AAC3B,mBAAKzB,aAAL,CAAmBC,MAAnB,GAA4B,KAA5B;AACH,aAFD,MAEO;AACHoB,cAAAA,cAAc,CAACZ,OAAf,GAAyB,CAAzB;AACAW,cAAAA,OAAO,CAACnB,MAAR,GAAiB,KAAjB;AACH;AACJ,WApBL,EAqBKa,KArBL;AAwBA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGK;AAID;AACJ;AACA;AACA;;;AACIa,QAAAA,SAAS,CAACC,OAAD,EAAkB;AACvB;AACA,cAAIA,OAAO,IAAI,EAAf,EAAmB;AACf,iBAAKC,kBAAL;AACH,WAJsB,CAMvB;;;AACA,cAAID,OAAO,IAAI,CAAX,IAAgBA,OAAO,GAAG,EAA9B,EAAkC;AAC9B,iBAAKE,IAAL;AACH;;AAED,cAAKF,OAAO,GAAG,CAAX,IAAkBA,OAAO,GAAG,CAAX,IAAiB,CAAtC,EAAyC;AACrC;AAAA;AAAA,0CAAWG,eAAX,CAA2B,KAAK7B,IAAhC,EAAsC,IAAtC,EAA4C,GAA5C,EAAiD,CAAjD;AACH;;AACD,cAAI0B,OAAO,IAAI,CAAf,EAAkB;AACd,iBAAKnE,QAAL,CAAc0C,OAAd,CAAsBX,GAAG,IAAI;AACzBA,cAAAA,GAAG,CAACvB,YAAJ;AAAA;AAAA,kCAAwB6B,IAAxB;AACAN,cAAAA,GAAG,CAACzB,IAAJ,CAASiE,QAAT,CAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAxB;AACH,aAHD;AAIH;;AAED,cAAMC,QAAQ,GAAG;AAAA;AAAA,0CAAYC,cAAZ,CAA2BN,OAA3B,CAAjB;AACA,eAAKO,cAAL,CAAoBjE,MAApB,GAA6B+D,QAA7B;AAEH;AAED;AACJ;AACA;AACA;;;AACWH,QAAAA,IAAI,GAAG;AACV,eAAKrE,QAAL,CAAc0C,OAAd,CAAsBpC,IAAI,IAAI;AAC1BjB,YAAAA,KAAK,CAACsD,eAAN,CAAsBrC,IAAtB;AACH,WAFD;AAGA,cAAMqE,IAAI,GAAG;AACTC,YAAAA,KAAK,EAAE;AADE,WAAb;AAGA3F,UAAAA,KAAK,CAAC0F,IAAD,CAAL,CACKzB,EADL,CACQ,GADR,EACa;AAAE0B,YAAAA,KAAK,EAAE;AAAT,WADb,EAEKb,IAFL,CAEU,MAAM;AACR,iBAAK/D,QAAL,CAAc6E,GAAd,CAAkB9C,GAAG,IAAIA,GAAG,CAACkC,KAAJ,EAAzB;AACH,WAJL,EAKKf,EALL,CAKQ,GALR,EAKa;AAAE0B,YAAAA,KAAK,EAAE;AAAT,WALb,EAMKb,IANL,CAMU,MAAM;AACR,iBAAK/D,QAAL,CAAc6E,GAAd,CAAkB9C,GAAG,IAAIA,GAAG,CAACM,IAAJ,EAAzB;AACH,WARL,EASKe,KATL,GAUKC,KAVL;AAWH;AAED;AACJ;AACA;AACA;;;AACYe,QAAAA,kBAAkB,GAAG;AACzB;AACA,cAAMU,YAAY,GAAG,EAArB;;AACA,eAAK,IAAIxD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKvB,UAAzB,EAAqCuB,CAAC,EAAtC,EAA0C;AACtCwD,YAAAA,YAAY,CAACxC,IAAb,CAAkB,CAAC,KAAKxC,YAAL,GAAoBwB,CAArB,IAA0B,KAAKtB,QAAL,CAAcgE,MAA1D;AACH,WALwB,CAOzB;;;AACA,cAAMe,YAAY,GAAG,EAArB;;AACA,eAAK,IAAIzD,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKvB,UAAzB,EAAqCuB,EAAC,EAAtC,EAA0C;AACtC,gBAAM0D,KAAK,GAAG,CAAC,KAAKlF,YAAL,GAAoB,KAAKC,UAAzB,GAAsCuB,EAAtC,GAA0C,KAAKtB,QAAL,CAAcgE,MAAzD,IAAmE,KAAKhE,QAAL,CAAcgE,MAA/F;AACAe,YAAAA,YAAY,CAACzC,IAAb,CAAkB0C,KAAlB;AACH,WAZwB,CAczB;;;AACAD,UAAAA,YAAY,CAACrC,OAAb,CAAqB,CAACsC,KAAD,EAAQ1D,CAAR,KAAc;AAC/BrC,YAAAA,KAAK,CAAC,KAAKe,QAAL,CAAcgF,KAAd,EAAqB1E,IAAtB,CAAL,CACKwD,KADL,CACWxC,CAAC,GAAG,GADf,EACoB;AADpB,aAEK4B,EAFL,CAEQ,GAFR,EAEa;AAAED,cAAAA,KAAK,EAAE,IAAI/D,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf;AAAT,aAFb,EAE2C;AAAE+F,cAAAA,MAAM,EAAE;AAAV,aAF3C,EAGKlB,IAHL,CAGU,MAAM,KAAK/D,QAAL,CAAcgF,KAAd,EAAqB3C,IAArB,EAHhB,EAIKgB,KAJL;AAKH,WAND,EAfyB,CAuBzB;;AACAyB,UAAAA,YAAY,CAACpC,OAAb,CAAqB,CAACsC,KAAD,EAAQ1D,CAAR,KAAc;AAC/BrC,YAAAA,KAAK,CAAC,KAAKe,QAAL,CAAcgF,KAAd,EAAqB1E,IAAtB,CAAL,CACKwD,KADL,CACWxC,CAAC,GAAG,GADf,EACoB;AADpB,aAEK4B,EAFL,CAEQ,GAFR,EAEa;AAAED,cAAAA,KAAK,EAAE,IAAI/D,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf;AAAT,aAFb,EAE2C;AAAE+F,cAAAA,MAAM,EAAE;AAAV,aAF3C,EAGKlB,IAHL,CAGU,MAAM,KAAK/D,QAAL,CAAcgF,KAAd,EAAqBf,KAArB,EAHhB,EAIKZ,KAJL;AAKH,WAND,EAxByB,CAgCzB;;AACA,eAAKvD,YAAL,GAAoB,CAAC,KAAKA,YAAL,GAAoB,KAAKC,UAA1B,IAAwC,KAAKC,QAAL,CAAcgE,MAA1E;AACH;AAED;AACJ;AACA;AACA;;;AACckB,QAAAA,QAAQ,GAAS,CAE1B;AAED;AACJ;AACA;AACA;;;AACcC,QAAAA,SAAS,GAAS;AACxB,eAAKC,sBAAL;AACH;AAGD;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AA7WwC,O;;;;;iBAGrB,I;;;;;;;iBAGY,I;;;;;;;iBAGX,I;;;;;;;iBAGD,I;;;;;;;iBAGU,I", "sourcesContent": ["import { _decorator, Component, easing, instantiate, Label, Node, Prefab, tween, UITransform, Vec3, Animation, UIOpacity, Tween } from 'cc';\r\nimport { Led } from './perfabs/Led';\r\nimport { TweenUtils } from '../framework/utils/TweenUtils';\r\nimport { FormatUtils } from '../framework/utils/FormatUtils';\r\nimport { NumberUtils } from '../framework/utils/NumberUtils';\r\nimport { BallItem } from './perfabs/BallItem';\r\nimport { eventManager } from '../framework/event/EventManager';\r\nimport { Pearl } from './perfabs/Pearl';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('NumberSpawner')\r\nexport class NumberSpawner extends Component {\r\n\r\n    @property(Prefab)\r\n    private led: Node = null;\r\n\r\n    @property(Label)\r\n    private countdownLabel: Label = null;\r\n\r\n    @property(Node)\r\n    private logo: Node = null;\r\n\r\n    @property(Node)\r\n    private bar: Node = null;\r\n\r\n    @property(Node)\r\n    private animationNode: Node = null;\r\n\r\n    private currentIndex: number = 0;\r\n    private lightCount: number = 3;\r\n\r\n    private ledNodes: Pearl[] = [];\r\n\r\n    private numbers: number[] = [];\r\n\r\n    private dataSource: any = null;\r\n\r\n    /**\r\n     * 设置数据源并更新UI显示\r\n     * @param data 包含游戏数据的对象，包含command、nextRound、currentRound等字段\r\n     */\r\n    setDataSource(data: any) {\r\n        this.dataSource = data;\r\n\r\n        if (data.command == 'ColorLuckyRecentResult') {\r\n            this.node.getChildByPath('/current/next').getComponent(Label).string = 'Next';\r\n            this.node.getChildByPath('/current/sn').getComponent(Label).string = `SN-${this.dataSource.nextRound}`;\r\n        }\r\n\r\n        // else {\r\n        //     this.node.getChildByPath('/current/next').getComponent(Label).string = 'Current';\r\n        //     this.node.getChildByPath('/current/sn').getComponent(Label).string = `SN-${this.dataSource.currentRound}`;\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 组件加载时的初始化方法\r\n     * 注册事件监听器并创建圆形排列的LED灯珠\r\n     */\r\n    onLoad() {\r\n        eventManager.on(\"BallsPanel_Finish\", this.onBallsFinish.bind(this));\r\n        eventManager.on(\"BallsPanel_Start\", this.onBallsStart.bind(this));\r\n\r\n        const centerX = 0;\r\n        const centerY = 0;\r\n        const radius = 210;\r\n        const numLights = 20;\r\n\r\n        const circleNode = this.node.getChildByName('current');\r\n\r\n        for (let i = numLights; i > 0; i--) {\r\n            const angle = (i / numLights) * Math.PI * 2 + Math.PI / 2;  // 将圆分成 20 等份 第一个节点在最上面\r\n            const x = centerX + radius * Math.cos(angle);\r\n            const y = centerY + radius * Math.sin(angle);\r\n\r\n            const ledNode = instantiate(this.led);\r\n\r\n            ledNode.setPosition(x, y);\r\n            // 设置旋转角度，将弧度转换为度数\r\n            const rotationAngle = (angle * 180 / Math.PI) - 90; // 减去90度是为了调整初始方向\r\n            ledNode.setRotationFromEuler(0, 0, rotationAngle);\r\n\r\n            circleNode.addChild(ledNode);\r\n\r\n            // 初始化所有灯为暗态\r\n            const ledComponent = ledNode.getComponent(Pearl);\r\n            ledComponent.dark();\r\n            this.ledNodes.push(ledComponent);\r\n        }\r\n\r\n    }\r\n\r\n    /**\r\n     * 球类游戏结束时的回调方法\r\n     * 重置游戏状态，清空数字数组，关闭动画，显示logo\r\n     */\r\n    onBallsFinish() {\r\n        this.numbers = [];\r\n        this.animationNode.active = false;\r\n        this.logo.active = true;\r\n        this.node.getChildByPath('/current/bigBall').active = false;\r\n        this.ledNodes.forEach(ledNode => {\r\n            Tween.stopAllByTarget(ledNode);\r\n            ledNode.dark();\r\n        });\r\n\r\n        this.node.getChildByPath('/current/next').getComponent(Label).string = 'Next';\r\n        this.node.getChildByPath('/current/sn').getComponent(Label).string = `SN-${this.dataSource.nextRound}`;\r\n    }\r\n\r\n    /**\r\n     * 球类游戏开始时的回调方法\r\n     * 重置LED状态，清空数字数组，隐藏logo和动画\r\n     */\r\n    onBallsStart() {\r\n        this.ledNodes.forEach(ledNode => {\r\n            Tween.stopAllByTarget(ledNode);\r\n            ledNode.dark();\r\n        });\r\n        this.numbers = [];\r\n        this.animationNode.active = false;\r\n        this.logo.active = false;\r\n        this.node.getChildByPath('/current/bigBall').active = false;\r\n\r\n        this.node.getChildByPath('/current/next').getComponent(Label).string = 'Current';\r\n        this.node.getChildByPath('/current/sn').getComponent(Label).string = `SN-${this.dataSource.currentRound}`;\r\n    }\r\n\r\n    /**\r\n     * 播放弹跳动画\r\n     * 激活动画节点并播放动画\r\n     */\r\n    public playBounceAnimation() {\r\n        this.animationNode.active = true;\r\n        this.animationNode.getComponent(Animation).play();\r\n    }\r\n\r\n    /**\r\n     * 按钮动画效果\r\n     * 显示大球的缩放和透明度动画效果\r\n     */\r\n    public btnAni() {\r\n        const opacity = this.node.getChildByPath('/current/bigBall').getComponent(UIOpacity);\r\n        // node.setScale(new Vec3(0.2, 0.2));\r\n        // node.setPosition(new Vec3(0, -170, 0));\r\n\r\n        opacity.opacity = 255;\r\n        opacity.node.scale = new Vec3(0.2, 0.2);\r\n        opacity.node.active = true;\r\n\r\n        tween(opacity.node)\r\n            .to(0.2, { scale: new Vec3(1.2, 1.2) })\r\n            .to(0.2, { scale: new Vec3(1, 1) })\r\n            .then(tween(opacity).to(2, { opacity: 0 }))\r\n            .union()\r\n            .start();\r\n    }\r\n\r\n    /**\r\n     * 设置默认状态\r\n     * 隐藏进度条，显示logo，关闭动画和大球\r\n     */\r\n    public setDefault() {\r\n        this.bar.active = false;\r\n        this.logo.active = true;\r\n        this.animationNode.active = false;\r\n        this.node.getChildByPath('/current/bigBall').active = false;\r\n    }\r\n\r\n    /**\r\n     * 显示大数字动画\r\n     * @param number 要显示的数字\r\n     * @param callback 动画完成后的回调函数\r\n     */\r\n    public showBigNumberAnimation(number: number, callback?: () => void) {\r\n        this.numbers.push(number);\r\n        this.animationNode.active = true;\r\n        // this.animationNode.getComponent(Animation).resume();\r\n\r\n        const bigBall = this.node.getChildByPath('/current/bigBall');\r\n        const bigBallOpacity = bigBall.getComponent(UIOpacity);\r\n        bigBall.getComponent(BallItem).setNumber(number);\r\n\r\n        Tween.stopAllByTarget(bigBall);\r\n        Tween.stopAllByTarget(bigBallOpacity);\r\n\r\n        bigBallOpacity.opacity = 255;\r\n        bigBall.scale = new Vec3(0, 0);\r\n        bigBall.active = true;\r\n\r\n        tween(bigBall)\r\n            .delay(0.5)\r\n            .to(0.15, { scale: new Vec3(1.2, 1.2) })\r\n            .to(0.15, { scale: new Vec3(1, 1) })\r\n            .call(() => {\r\n                this.ledNodes[this.numbers.length - 1].light();\r\n                if (callback) {\r\n                    callback();\r\n                }\r\n            })\r\n            .delay(0.7)\r\n            // .then(tween(bigBallOpacity).to(0.2, { opacity: 0 }))\r\n            .union()\r\n            .call(() => {\r\n                if (this.numbers.length == 20) {\r\n                    this.animationNode.active = false;\r\n                } else {\r\n                    bigBallOpacity.opacity = 0;\r\n                    bigBall.active = false;\r\n                }\r\n            })\r\n            .start();\r\n\r\n\r\n        /*\r\n        if (this.numbers.length == 20) {\r\n            tween(bigBall)\r\n                .to(0.15, { scale: new Vec3(1.2, 1.2) })\r\n                .to(0.2, { scale: new Vec3(1, 1) })\r\n                .call(() => {\r\n\r\n                    if (callback) {\r\n                        callback();\r\n                    }\r\n\r\n                    this.tick();\r\n                })\r\n                .delay(1)\r\n                .union()\r\n                .call(() => {\r\n                    this.animationNode.active = false;\r\n                    // this.showBarAnimation(number);\r\n                })\r\n                .start();\r\n        } else {\r\n            tween(bigBall)\r\n                .to(0.15, { scale: new Vec3(1.2, 1.2) })\r\n                .to(0.2, { scale: new Vec3(1, 1) })\r\n                .call(() => {\r\n                    this.ledNodes[this.numbers.length - 1].light();\r\n                    if (callback) {\r\n                        callback();\r\n                    }\r\n                })\r\n                .delay(1)\r\n                .then(tween(bigBallOpacity).to(0.2, { opacity: 0 }))\r\n                .union()\r\n                .call(() => {\r\n                    bigBallOpacity.opacity = 0;\r\n                    bigBall.active = false;\r\n                })\r\n                .start();\r\n        }\r\n        */\r\n    }\r\n\r\n\r\n\r\n    /**\r\n     * 倒计时功能\r\n     * @param seconds 倒计时秒数\r\n     */\r\n    countdown(seconds: number) {\r\n        //流水灯显示\r\n        if (seconds >= 20) {\r\n            this.startFlowAnimation();\r\n        }\r\n\r\n        //  倒计时小于20秒时，开始闪烁led\r\n        if (seconds >= 1 && seconds < 20) {\r\n            this.tick();\r\n        }\r\n\r\n        if ((seconds > 0) && (seconds % 5) == 0) {\r\n            TweenUtils.scaleAndRestore(this.logo, 1.15, 0.3, 0);\r\n        }\r\n        if (seconds <= 0) {\r\n            this.ledNodes.forEach(led => { \r\n                led.getComponent(Pearl).dark();\r\n                led.node.setScale(1, 1, 1);\r\n            });\r\n        }\r\n\r\n        const duration = FormatUtils.formatDuration(seconds);\r\n        this.countdownLabel.string = duration;\r\n\r\n    }\r\n\r\n    /**\r\n     * LED灯珠闪烁效果\r\n     * 让所有LED灯珠同时闪烁一次\r\n     */\r\n    public tick() {\r\n        this.ledNodes.forEach(node => {\r\n            Tween.stopAllByTarget(node);\r\n        });\r\n        const objs = {\r\n            value: 0\r\n        };\r\n        tween(objs)\r\n            .to(0.4, { value: 0 })\r\n            .call(() => {\r\n                this.ledNodes.map(led => led.light());\r\n            })\r\n            .to(0.4, { value: 0 })\r\n            .call(() => {\r\n                this.ledNodes.map(led => led.dark());\r\n            })\r\n            .union()\r\n            .start();\r\n    }\r\n\r\n    /**\r\n     * 流水灯动画效果\r\n     * 创建连续的LED灯珠流动效果，每次点亮3个连续的灯珠\r\n     */\r\n    private startFlowAnimation() {\r\n        // 计算当前要亮起的 3 个灯珠索引\r\n        const lightsToShow = [];\r\n        for (let i = 0; i < this.lightCount; i++) {\r\n            lightsToShow.push((this.currentIndex + i) % this.ledNodes.length);\r\n        }\r\n\r\n        // 计算上一轮亮起的 3 个灯珠（即当前 - lightCount 到当前 -1）\r\n        const lightsToHide = [];\r\n        for (let i = 0; i < this.lightCount; i++) {\r\n            const index = (this.currentIndex - this.lightCount + i + this.ledNodes.length) % this.ledNodes.length;\r\n            lightsToHide.push(index);\r\n        }\r\n\r\n        // 逐个熄灭之前的 3 个灯珠\r\n        lightsToHide.forEach((index, i) => {\r\n            tween(this.ledNodes[index].node)\r\n                .delay(i * 0.2) // 延迟熄灭时间，与点亮同步\r\n                .to(0.2, { scale: new Vec3(1, 1, 1) }, { easing: 'backIn' })\r\n                .call(() => this.ledNodes[index].dark())\r\n                .start();\r\n        });\r\n\r\n        // 逐个点亮新的 3 个灯珠\r\n        lightsToShow.forEach((index, i) => {\r\n            tween(this.ledNodes[index].node)\r\n                .delay(i * 0.2) // 依次点亮\r\n                .to(0.2, { scale: new Vec3(1, 1, 1) }, { easing: 'backOut' })\r\n                .call(() => this.ledNodes[index].light())\r\n                .start();\r\n        });\r\n\r\n        // 更新当前索引\r\n        this.currentIndex = (this.currentIndex + this.lightCount) % this.ledNodes.length;\r\n    }\r\n\r\n    /**\r\n     * 组件启用时的回调方法\r\n     * 当前为空实现，可在此添加启用时的逻辑\r\n     */\r\n    protected onEnable(): void {\r\n\r\n    }\r\n\r\n    /**\r\n     * 组件禁用时的回调方法\r\n     * 取消所有定时器回调\r\n     */\r\n    protected onDisable(): void {\r\n        this.unscheduleAllCallbacks();\r\n    }\r\n\r\n\r\n    /**\r\n     * 每帧更新方法\r\n     * @param deltaTime 帧间隔时间\r\n     * 当前为空实现，可在此添加每帧需要执行的逻辑\r\n     */\r\n    update(deltaTime: number) {\r\n\r\n    }\r\n}\r\n\r\n\r\n"]}