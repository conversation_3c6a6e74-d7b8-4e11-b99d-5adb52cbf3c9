[{"__type__": "cc.Prefab", "_name": "suggestPanel", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "suggestPanel", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 14}, {"__id__": 90}, {"__id__": 144}], "_active": true, "_components": [{"__id__": 220}, {"__id__": 222}], "_prefab": {"__id__": 224}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.667, "y": 0.667, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_active": true, "_components": [{"__id__": 9}, {"__id__": 11}], "_prefab": {"__id__": 13}, "_lpos": {"__type__": "cc.Vec3", "x": 330.793, "y": 366.049, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "line", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}], "_prefab": {"__id__": 8}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -45.217, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_contentSize": {"__type__": "cc.Size", "width": 550, "height": 2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ac6daIFhlJ6Lws4wMTCKgG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c36eUrAQRDApYhG3hu+puA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a3I5IuROFC15mp0geXLJnG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 10}, "_contentSize": {"__type__": "cc.Size", "width": 497.7274130981276, "height": 100.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "860xHk/MZJIoGUErLeelqw"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 12}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "HOT & COLD", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 80.11111111111111, "_fontSize": 80, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 80, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 96, "g": 73, "b": 178, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -5}, "_shadowBlur": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4b1vzI8CtFxZRHr1i+uZJI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "59u4ZU9YdLe5RK0rTtCkDz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "fire", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 15}], "_active": true, "_components": [{"__id__": 85}, {"__id__": 87}], "_prefab": {"__id__": 89}, "_lpos": {"__type__": "cc.Vec3", "x": 60, "y": 205, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "balls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 14}, "_children": [{"__id__": 16}, {"__id__": 24}, {"__id__": 32}, {"__id__": 40}, {"__id__": 48}, {"__id__": 56}, {"__id__": 64}, {"__id__": 72}], "_active": true, "_components": [{"__id__": 80}, {"__id__": 82}], "_prefab": {"__id__": 84}, "_lpos": {"__type__": "cc.Vec3", "x": 8, "y": -312, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 15}, "_prefab": {"__id__": 17}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "d1e2e19a-e5af-4e81-a15d-4d6dff4a5fd5", "__expectedType__": "cc.Prefab"}, "fileId": "02K4gaCZNFHqu6aJrayAlW", "instance": {"__id__": 18}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "d5PABlGbxPU44IBx9D/hUI", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 19}, {"__id__": 21}, {"__id__": 22}, {"__id__": 23}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 20}, "propertyPath": ["_name"], "value": "ball"}, {"__type__": "cc.TargetInfo", "localID": ["02K4gaCZNFHqu6aJrayAlW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 20}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -67, "y": 187.5, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 20}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 20}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 15}, "_prefab": {"__id__": 25}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 24}, "asset": {"__uuid__": "d1e2e19a-e5af-4e81-a15d-4d6dff4a5fd5", "__expectedType__": "cc.Prefab"}, "fileId": "02K4gaCZNFHqu6aJrayAlW", "instance": {"__id__": 26}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "c82bZcwe5Bp6Ul8Kym94yM", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 27}, {"__id__": 29}, {"__id__": 30}, {"__id__": 31}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 28}, "propertyPath": ["_name"], "value": "ball"}, {"__type__": "cc.TargetInfo", "localID": ["02K4gaCZNFHqu6aJrayAlW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 28}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 67, "y": 187.5, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 28}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 28}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 15}, "_prefab": {"__id__": 33}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 32}, "asset": {"__uuid__": "d1e2e19a-e5af-4e81-a15d-4d6dff4a5fd5", "__expectedType__": "cc.Prefab"}, "fileId": "02K4gaCZNFHqu6aJrayAlW", "instance": {"__id__": 34}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "e2iUeiMStOlLIAEomp3V0I", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 35}, {"__id__": 37}, {"__id__": 38}, {"__id__": 39}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 36}, "propertyPath": ["_name"], "value": "ball"}, {"__type__": "cc.TargetInfo", "localID": ["02K4gaCZNFHqu6aJrayAlW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 36}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -67, "y": 62.5, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 36}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 36}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 15}, "_prefab": {"__id__": 41}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 40}, "asset": {"__uuid__": "d1e2e19a-e5af-4e81-a15d-4d6dff4a5fd5", "__expectedType__": "cc.Prefab"}, "fileId": "02K4gaCZNFHqu6aJrayAlW", "instance": {"__id__": 42}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "23KLF2ovdJBIa3fMm4QlO6", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 43}, {"__id__": 45}, {"__id__": 46}, {"__id__": 47}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 44}, "propertyPath": ["_name"], "value": "ball"}, {"__type__": "cc.TargetInfo", "localID": ["02K4gaCZNFHqu6aJrayAlW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 44}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 67, "y": 62.5, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 44}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 44}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 15}, "_prefab": {"__id__": 49}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 48}, "asset": {"__uuid__": "d1e2e19a-e5af-4e81-a15d-4d6dff4a5fd5", "__expectedType__": "cc.Prefab"}, "fileId": "02K4gaCZNFHqu6aJrayAlW", "instance": {"__id__": 50}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "bdxcU00elHoLKX2hlLGKW3", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 51}, {"__id__": 53}, {"__id__": 54}, {"__id__": 55}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 52}, "propertyPath": ["_name"], "value": "ball"}, {"__type__": "cc.TargetInfo", "localID": ["02K4gaCZNFHqu6aJrayAlW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 52}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -67, "y": -62.5, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 52}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 52}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 15}, "_prefab": {"__id__": 57}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 56}, "asset": {"__uuid__": "d1e2e19a-e5af-4e81-a15d-4d6dff4a5fd5", "__expectedType__": "cc.Prefab"}, "fileId": "02K4gaCZNFHqu6aJrayAlW", "instance": {"__id__": 58}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "2b2f4n0epN+Yn9FPcdddPV", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 59}, {"__id__": 61}, {"__id__": 62}, {"__id__": 63}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 60}, "propertyPath": ["_name"], "value": "ball"}, {"__type__": "cc.TargetInfo", "localID": ["02K4gaCZNFHqu6aJrayAlW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 60}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 67, "y": -62.5, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 60}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 60}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 15}, "_prefab": {"__id__": 65}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "d1e2e19a-e5af-4e81-a15d-4d6dff4a5fd5", "__expectedType__": "cc.Prefab"}, "fileId": "02K4gaCZNFHqu6aJrayAlW", "instance": {"__id__": 66}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "796lBwI01Bv7uh4YiY8rfL", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 67}, {"__id__": 69}, {"__id__": 70}, {"__id__": 71}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 68}, "propertyPath": ["_name"], "value": "ball"}, {"__type__": "cc.TargetInfo", "localID": ["02K4gaCZNFHqu6aJrayAlW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 68}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -67, "y": -187.5, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 68}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 68}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 15}, "_prefab": {"__id__": 73}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 72}, "asset": {"__uuid__": "d1e2e19a-e5af-4e81-a15d-4d6dff4a5fd5", "__expectedType__": "cc.Prefab"}, "fileId": "02K4gaCZNFHqu6aJrayAlW", "instance": {"__id__": 74}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "dekDWSRilAn6PBzZSNrZdt", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 75}, {"__id__": 77}, {"__id__": 78}, {"__id__": 79}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 76}, "propertyPath": ["_name"], "value": "ball"}, {"__type__": "cc.TargetInfo", "localID": ["02K4gaCZNFHqu6aJrayAlW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 76}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 67, "y": -187.5, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 76}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 76}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 81}, "_contentSize": {"__type__": "cc.Size", "width": 258, "height": 500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dNQNfuABHj415mirwp3hE"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 83}, "_resizeMode": 0, "_layoutType": 3, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 10, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0WDHueFRPoKiEab/0XeE7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ba1WFmbcFIoa5ozxQyl29J", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 86}, "_contentSize": {"__type__": "cc.Size", "width": 73, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51IrDWFh5EsrZOYtGQS35x"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 88}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4d2a7918-786f-4e4f-aeaf-f1db580637e2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2FmEuEOxHwaw/1IMBkTbk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "54rzkbFUJKG7eQ9z4OH4X6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "block", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 91}, {"__id__": 103}, {"__id__": 115}, {"__id__": 127}], "_active": true, "_components": [{"__id__": 139}, {"__id__": 141}], "_prefab": {"__id__": 143}, "_lpos": {"__type__": "cc.Vec3", "x": 290, "y": -75, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "blue", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 90}, "_children": [{"__id__": 92}], "_active": true, "_components": [{"__id__": 98}, {"__id__": 100}], "_prefab": {"__id__": 102}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 184, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 91}, "_children": [], "_active": true, "_components": [{"__id__": 93}, {"__id__": 95}], "_prefab": {"__id__": 97}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 92}, "_enabled": true, "__prefab": {"__id__": 94}, "_contentSize": {"__type__": "cc.Size", "width": 21.132240242501734, "height": 56.7}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2mcO0w4hAQpptuvBRD8W7"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 92}, "_enabled": true, "__prefab": {"__id__": 96}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 38.05277777777778, "_fontSize": 38, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 45, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 93, "g": 65, "b": 191, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 1, "y": -2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9b2Eygf5FI9Z8vodc0k9Zb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "31MGExVI1FjqYYpXoy4f4e", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 91}, "_enabled": true, "__prefab": {"__id__": 99}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deMJBYoC1M3a5QoIapc9Jb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 91}, "_enabled": true, "__prefab": {"__id__": 101}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e83c1859-e145-4ad3-bb38-536eba6abd0b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33zp6BjSVPC7j8/u4RgCEJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "281e8r4MBGwoRo8NzMa+fB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "orange", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 90}, "_children": [{"__id__": 104}], "_active": true, "_components": [{"__id__": 110}, {"__id__": 112}], "_prefab": {"__id__": 114}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 57, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 103}, "_children": [], "_active": true, "_components": [{"__id__": 105}, {"__id__": 107}], "_prefab": {"__id__": 109}, "_lpos": {"__type__": "cc.Vec3", "x": -2.2737367544323206e-13, "y": -40, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 104}, "_enabled": true, "__prefab": {"__id__": 106}, "_contentSize": {"__type__": "cc.Size", "width": 21.132240242501734, "height": 56.7}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aa+PvO98hIdIF2PuJ6vK3d"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 104}, "_enabled": true, "__prefab": {"__id__": 108}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 38.05277777777778, "_fontSize": 38, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 45, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 93, "g": 65, "b": 191, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 1, "y": -2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1eotdrTEJCGLj0ALVJ06kW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "82NakmL/tIHYMopvdmz7h3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 103}, "_enabled": true, "__prefab": {"__id__": 111}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5b9rgdy3lCApDPPrxhqsmG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 103}, "_enabled": true, "__prefab": {"__id__": 113}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "26b9fe40-f949-4c76-951c-a0e9f6dd45a4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b9gXXubIJBrJ7NSsnFu4lQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "90FU0CBkdF2oM5buXB6sRt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "purple", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 90}, "_children": [{"__id__": 116}], "_active": true, "_components": [{"__id__": 122}, {"__id__": 124}], "_prefab": {"__id__": 126}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -70, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 115}, "_children": [], "_active": true, "_components": [{"__id__": 117}, {"__id__": 119}], "_prefab": {"__id__": 121}, "_lpos": {"__type__": "cc.Vec3", "x": -2.2737367544323206e-13, "y": -40, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 116}, "_enabled": true, "__prefab": {"__id__": 118}, "_contentSize": {"__type__": "cc.Size", "width": 21.132240242501734, "height": 56.7}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b95U1vWjxCgaoFd42/RN1j"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 116}, "_enabled": true, "__prefab": {"__id__": 120}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 38.05277777777778, "_fontSize": 38, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 45, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 93, "g": 65, "b": 191, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 1, "y": -2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8njn10lRPxKgVLODelmfM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a1cL7sOBdC8oaoi6Cd0uv6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 115}, "_enabled": true, "__prefab": {"__id__": 123}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cPyh0frJPl7pOjwCNG94d"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 115}, "_enabled": true, "__prefab": {"__id__": 125}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5eb44984-0e38-4a7a-ae70-58ba71885b3d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33iA0Ks0FI2KPl+xetPy5X"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c3OyxEsZ9A/Jcfe7Yqo4md", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "green", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 90}, "_children": [{"__id__": 128}], "_active": true, "_components": [{"__id__": 134}, {"__id__": 136}], "_prefab": {"__id__": 138}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -197, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 127}, "_children": [], "_active": true, "_components": [{"__id__": 129}, {"__id__": 131}], "_prefab": {"__id__": 133}, "_lpos": {"__type__": "cc.Vec3", "x": -2.2737367544323206e-13, "y": -40, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 128}, "_enabled": true, "__prefab": {"__id__": 130}, "_contentSize": {"__type__": "cc.Size", "width": 21.132240242501734, "height": 56.7}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50FJ1wl5JDbrZB0oBX5wj8"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 128}, "_enabled": true, "__prefab": {"__id__": 132}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 38.05277777777778, "_fontSize": 38, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 45, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 93, "g": 65, "b": 191, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 1, "y": -2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "99gPJYG/lEoq0waKopaL3g"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5daNfsMIJHxZ0bHjiuVtS9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 127}, "_enabled": true, "__prefab": {"__id__": 135}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3bRXQB5ZDTql64ZTiFzNZ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 127}, "_enabled": true, "__prefab": {"__id__": 137}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7dac40ed-81fd-4efc-bcba-5b6c8c1b0dd5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "170DnlTe9Dp4vGshmOt9fN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "40SJk4kJpGT6nEw0Rf4zGb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 90}, "_enabled": true, "__prefab": {"__id__": 140}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fa8FxSFdBCJ6ZGwWer2ny"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 90}, "_enabled": true, "__prefab": {"__id__": 142}, "_resizeMode": 0, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 95, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ceZ/BdRbpD2rY14ToGP32h"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f8nrlNbX9E/4RfMmOHZ9MC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "snowflake", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 145}], "_active": true, "_components": [{"__id__": 215}, {"__id__": 217}], "_prefab": {"__id__": 219}, "_lpos": {"__type__": "cc.Vec3", "x": 520, "y": 205, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "balls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 144}, "_children": [{"__id__": 146}, {"__id__": 154}, {"__id__": 162}, {"__id__": 170}, {"__id__": 178}, {"__id__": 186}, {"__id__": 194}, {"__id__": 202}], "_active": true, "_components": [{"__id__": 210}, {"__id__": 212}], "_prefab": {"__id__": 214}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -312, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 145}, "_prefab": {"__id__": 147}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 146}, "asset": {"__uuid__": "d1e2e19a-e5af-4e81-a15d-4d6dff4a5fd5", "__expectedType__": "cc.Prefab"}, "fileId": "02K4gaCZNFHqu6aJrayAlW", "instance": {"__id__": 148}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "e2KV4KuxVJraVynf72Etd4", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 149}, {"__id__": 151}, {"__id__": 152}, {"__id__": 153}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 150}, "propertyPath": ["_name"], "value": "ball"}, {"__type__": "cc.TargetInfo", "localID": ["02K4gaCZNFHqu6aJrayAlW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 150}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -67, "y": 187.5, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 150}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 150}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 145}, "_prefab": {"__id__": 155}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 154}, "asset": {"__uuid__": "d1e2e19a-e5af-4e81-a15d-4d6dff4a5fd5", "__expectedType__": "cc.Prefab"}, "fileId": "02K4gaCZNFHqu6aJrayAlW", "instance": {"__id__": 156}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "33Z6rpLwBExKpFl2N1WfMN", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 157}, {"__id__": 159}, {"__id__": 160}, {"__id__": 161}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 158}, "propertyPath": ["_name"], "value": "ball"}, {"__type__": "cc.TargetInfo", "localID": ["02K4gaCZNFHqu6aJrayAlW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 158}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 67, "y": 187.5, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 158}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 158}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 145}, "_prefab": {"__id__": 163}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 162}, "asset": {"__uuid__": "d1e2e19a-e5af-4e81-a15d-4d6dff4a5fd5", "__expectedType__": "cc.Prefab"}, "fileId": "02K4gaCZNFHqu6aJrayAlW", "instance": {"__id__": 164}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "a6GH3+lw9E06W4BEPBABMP", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 165}, {"__id__": 167}, {"__id__": 168}, {"__id__": 169}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 166}, "propertyPath": ["_name"], "value": "ball"}, {"__type__": "cc.TargetInfo", "localID": ["02K4gaCZNFHqu6aJrayAlW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 166}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -67, "y": 62.5, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 166}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 166}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 145}, "_prefab": {"__id__": 171}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 170}, "asset": {"__uuid__": "d1e2e19a-e5af-4e81-a15d-4d6dff4a5fd5", "__expectedType__": "cc.Prefab"}, "fileId": "02K4gaCZNFHqu6aJrayAlW", "instance": {"__id__": 172}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "adQI+GOhZPA4UN29fFcnWV", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 173}, {"__id__": 175}, {"__id__": 176}, {"__id__": 177}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 174}, "propertyPath": ["_name"], "value": "ball"}, {"__type__": "cc.TargetInfo", "localID": ["02K4gaCZNFHqu6aJrayAlW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 174}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 67, "y": 62.5, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 174}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 174}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 145}, "_prefab": {"__id__": 179}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 178}, "asset": {"__uuid__": "d1e2e19a-e5af-4e81-a15d-4d6dff4a5fd5", "__expectedType__": "cc.Prefab"}, "fileId": "02K4gaCZNFHqu6aJrayAlW", "instance": {"__id__": 180}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "b0+VwEAthLxYTBZRWLRpPb", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 181}, {"__id__": 183}, {"__id__": 184}, {"__id__": 185}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 182}, "propertyPath": ["_name"], "value": "ball"}, {"__type__": "cc.TargetInfo", "localID": ["02K4gaCZNFHqu6aJrayAlW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 182}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -67, "y": -62.5, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 182}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 182}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 145}, "_prefab": {"__id__": 187}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 186}, "asset": {"__uuid__": "d1e2e19a-e5af-4e81-a15d-4d6dff4a5fd5", "__expectedType__": "cc.Prefab"}, "fileId": "02K4gaCZNFHqu6aJrayAlW", "instance": {"__id__": 188}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "69YJL02q9EpLn4PhCsr5nl", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 189}, {"__id__": 191}, {"__id__": 192}, {"__id__": 193}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 190}, "propertyPath": ["_name"], "value": "ball"}, {"__type__": "cc.TargetInfo", "localID": ["02K4gaCZNFHqu6aJrayAlW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 190}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 67, "y": -62.5, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 190}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 190}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 145}, "_prefab": {"__id__": 195}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 194}, "asset": {"__uuid__": "d1e2e19a-e5af-4e81-a15d-4d6dff4a5fd5", "__expectedType__": "cc.Prefab"}, "fileId": "02K4gaCZNFHqu6aJrayAlW", "instance": {"__id__": 196}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "89Vo65szRMm5Q91H/cMett", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 197}, {"__id__": 199}, {"__id__": 200}, {"__id__": 201}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 198}, "propertyPath": ["_name"], "value": "ball"}, {"__type__": "cc.TargetInfo", "localID": ["02K4gaCZNFHqu6aJrayAlW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 198}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -67, "y": -187.5, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 198}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 198}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 145}, "_prefab": {"__id__": 203}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 202}, "asset": {"__uuid__": "d1e2e19a-e5af-4e81-a15d-4d6dff4a5fd5", "__expectedType__": "cc.Prefab"}, "fileId": "02K4gaCZNFHqu6aJrayAlW", "instance": {"__id__": 204}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "11448BwyhDQIT9H+CGaGGy", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 205}, {"__id__": 207}, {"__id__": 208}, {"__id__": 209}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 206}, "propertyPath": ["_name"], "value": "ball"}, {"__type__": "cc.TargetInfo", "localID": ["02K4gaCZNFHqu6aJrayAlW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 206}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 67, "y": -187.5, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 206}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 206}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": {"__id__": 211}, "_contentSize": {"__type__": "cc.Size", "width": 258, "height": 500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30UAlNn4VL86LpFiCVZz99"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": {"__id__": 213}, "_resizeMode": 0, "_layoutType": 3, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 10, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8fc5sWO5DFYFAFVPB5SEF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ddTehEviRIbJ42XlUkhA4H", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 144}, "_enabled": true, "__prefab": {"__id__": 216}, "_contentSize": {"__type__": "cc.Size", "width": 86, "height": 96}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b55Jd1rBpJVbTJB0hFQF2k"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 144}, "_enabled": true, "__prefab": {"__id__": 218}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f2b9d8ac-aca8-4bc4-929e-fc73a051c871@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58g/A6cwBHTKJYdBarZPfs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a1L1HTeJlM2qxhRAMFRACF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 221}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fcjdwUtOBKWKoHeg4+LpuN"}, {"__type__": "bea15KkWtpJ+Y7AVpZS7Trn", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 223}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2/nmQ0/hF6JBSFFuMAwgl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a7Ql7Tr5RBJbeXSFr8sVyw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 202}, {"__id__": 194}, {"__id__": 186}, {"__id__": 178}, {"__id__": 170}, {"__id__": 162}, {"__id__": 154}, {"__id__": 146}, {"__id__": 72}, {"__id__": 64}, {"__id__": 56}, {"__id__": 48}, {"__id__": 40}, {"__id__": 32}, {"__id__": 24}, {"__id__": 16}]}]