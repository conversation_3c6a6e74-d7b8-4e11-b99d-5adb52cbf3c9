import { _decorator, Component, instantiate, Label, Node, Prefab, Tween } from 'cc';
import { TweenUtils } from '../../framework/utils/TweenUtils';
import { NumberUtils } from '../../framework/utils/NumberUtils';
import { BallItem } from '../perfabs/BallItem';
const { ccclass, property } = _decorator;

@ccclass('SuggestPanel')
export class SuggestPanel extends Component {

        
    private dataSource: any = null;

    setDataSource(data: any) {
        this.dataSource = data;
    }


    start() {



    }

    /**
     * 显示热门冷门号码
     */
    private displayHotAndCold() { 

        const numbers = this.dataSource.recentResult.reduce((prev, curr) => {
            return prev.concat(curr.result);
        }, []);

        const topBottom = NumberUtils.getTopAndBottomNumbers(numbers);

        const colorCount = NumberUtils.getColorCount(numbers);

        const data = {
            hot: topBottom.top10,
            cold: topBottom.bottom10,
            colors: colorCount
        }

        console.log("topBottom",data);

        this.node.getChildByPath('/fire/balls').children.map((node, idx) => {
            node.getComponent(BallItem).setNumber(data.hot[idx].number);
        });

        this.node.getChildByPath('/snowflake/balls').children.map((node, idx) => {
            node.getComponent(BallItem).setNumber(data.cold[idx].number);
        });

        this.node.getChildByPath('/block').children.map((node, idx) => {
            node.getChildByName('text').getComponent(Label).string = (data.colors[idx]).toString();
        });
    }


    protected onEnable(): void {
        this.displayHotAndCold();

        this.scheduleOnce(() => {
            this.showAnimation();
        }, 5);
    }


    public showAnimation() {
        const balls = [];
        this.node.getChildByPath('/fire/balls').children.map(node => {
            balls.push(node);
        });

        this.node.getChildByPath('/snowflake/balls').children.map(node => {
            balls.push(node);
        });

        //动画总时长
        const totalDuration = 1;
        //每个球的动画时长
        const duration = totalDuration / balls.length;

        balls.forEach((ball, idx) => {
            const delay = duration * idx * 0.75;
            TweenUtils.scaleAndRestore(ball, 1.2, duration, delay);
        });
    }

    protected onDisable(): void {
        this.unscheduleAllCallbacks();
        
        this.node.getChildByPath('/fire/balls').children.forEach(node => {
           Tween.stopAllByTarget(node);
        });
        this.node.getChildByPath('/snowflake/balls').children.forEach(node => {
            Tween.stopAllByTarget(node);
        });
    }

    update(deltaTime: number) {

    }
}


