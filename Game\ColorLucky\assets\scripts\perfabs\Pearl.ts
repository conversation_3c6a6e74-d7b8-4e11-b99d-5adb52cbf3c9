import { _decorator, Component, Node } from 'cc';
import { TweenUtils } from '../../framework/utils/TweenUtils';
const { ccclass, property } = _decorator;

@ccclass('Pearl')
export class Pearl extends Component {
    start() {

    }

    update(deltaTime: number) {
        
    }

    light() {
        TweenUtils.fadeIn(this.node.getChildByName('light'), 0.1);
        // this.node.getChildByName('light').active = true;
    }

    dark() {
        TweenUtils.fadeOut(this.node.getChildByName('light'), 0.1);
        // this.node.getChildByName('light').active = false;
    }
}


