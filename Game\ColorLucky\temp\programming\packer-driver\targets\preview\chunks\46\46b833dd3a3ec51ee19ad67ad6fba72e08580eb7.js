System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, instantiate, Label, Node, Prefab, tween, Vec3, Animation, UIOpacity, Tween, TweenUtils, FormatUtils, BallItem, eventManager, Pearl, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, NumberSpawner;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfTweenUtils(extras) {
    _reporterNs.report("TweenUtils", "../framework/utils/TweenUtils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFormatUtils(extras) {
    _reporterNs.report("FormatUtils", "../framework/utils/FormatUtils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBallItem(extras) {
    _reporterNs.report("BallItem", "./perfabs/BallItem", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeventManager(extras) {
    _reporterNs.report("eventManager", "../framework/event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPearl(extras) {
    _reporterNs.report("Pearl", "./perfabs/Pearl", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
      Label = _cc.Label;
      Node = _cc.Node;
      Prefab = _cc.Prefab;
      tween = _cc.tween;
      Vec3 = _cc.Vec3;
      Animation = _cc.Animation;
      UIOpacity = _cc.UIOpacity;
      Tween = _cc.Tween;
    }, function (_unresolved_2) {
      TweenUtils = _unresolved_2.TweenUtils;
    }, function (_unresolved_3) {
      FormatUtils = _unresolved_3.FormatUtils;
    }, function (_unresolved_4) {
      BallItem = _unresolved_4.BallItem;
    }, function (_unresolved_5) {
      eventManager = _unresolved_5.eventManager;
    }, function (_unresolved_6) {
      Pearl = _unresolved_6.Pearl;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "9e017+FgvlMqrrEIhB1ODEp", "NumberSpawner", undefined);

      __checkObsolete__(['_decorator', 'Component', 'easing', 'instantiate', 'Label', 'Node', 'Prefab', 'tween', 'UITransform', 'Vec3', 'Animation', 'UIOpacity', 'Tween']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("NumberSpawner", NumberSpawner = (_dec = ccclass('NumberSpawner'), _dec2 = property(Prefab), _dec3 = property(Label), _dec4 = property(Node), _dec5 = property(Node), _dec6 = property(Node), _dec(_class = (_class2 = class NumberSpawner extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "led", _descriptor, this);

          _initializerDefineProperty(this, "countdownLabel", _descriptor2, this);

          _initializerDefineProperty(this, "logo", _descriptor3, this);

          _initializerDefineProperty(this, "bar", _descriptor4, this);

          _initializerDefineProperty(this, "animationNode", _descriptor5, this);

          this.currentIndex = 0;
          this.lightCount = 3;
          this.ledNodes = [];
          this.numbers = [];
          this.dataSource = null;
        }

        setDataSource(data) {
          this.dataSource = data;

          if (data.command == 'ColorLuckyRecentResult') {
            this.node.getChildByPath('/current/next').getComponent(Label).string = 'Next';
            this.node.getChildByPath('/current/sn').getComponent(Label).string = "SN-" + this.dataSource.nextRound;
          } // else {
          //     this.node.getChildByPath('/current/next').getComponent(Label).string = 'Current';
          //     this.node.getChildByPath('/current/sn').getComponent(Label).string = `SN-${this.dataSource.currentRound}`;
          // }

        }

        onLoad() {
          (_crd && eventManager === void 0 ? (_reportPossibleCrUseOfeventManager({
            error: Error()
          }), eventManager) : eventManager).on("BallsPanel_Finish", this.onBallsFinish.bind(this));
          (_crd && eventManager === void 0 ? (_reportPossibleCrUseOfeventManager({
            error: Error()
          }), eventManager) : eventManager).on("BallsPanel_Start", this.onBallsStart.bind(this));
          var centerX = 0;
          var centerY = 0;
          var radius = 210;
          var numLights = 20;
          var circleNode = this.node.getChildByName('current');

          for (var i = numLights; i > 0; i--) {
            var angle = i / numLights * Math.PI * 2 + Math.PI / 2; // 将圆分成 20 等份 第一个节点在最上面

            var x = centerX + radius * Math.cos(angle);
            var y = centerY + radius * Math.sin(angle);
            var ledNode = instantiate(this.led);
            ledNode.setPosition(x, y); // 设置旋转角度，将弧度转换为度数

            var rotationAngle = angle * 180 / Math.PI - 90; // 减去90度是为了调整初始方向

            ledNode.setRotationFromEuler(0, 0, rotationAngle);
            circleNode.addChild(ledNode); // 初始化所有灯为暗态

            var ledComponent = ledNode.getComponent(_crd && Pearl === void 0 ? (_reportPossibleCrUseOfPearl({
              error: Error()
            }), Pearl) : Pearl);
            ledComponent.dark();
            this.ledNodes.push(ledComponent);
          }
        }

        onBallsFinish() {
          this.numbers = [];
          this.animationNode.active = false;
          this.logo.active = true;
          this.node.getChildByPath('/current/bigBall').active = false;
          this.ledNodes.forEach(ledNode => {
            Tween.stopAllByTarget(ledNode);
            ledNode.dark();
          });
          this.node.getChildByPath('/current/next').getComponent(Label).string = 'Next';
          this.node.getChildByPath('/current/sn').getComponent(Label).string = "SN-" + this.dataSource.nextRound;
        }

        onBallsStart() {
          this.ledNodes.forEach(ledNode => {
            Tween.stopAllByTarget(ledNode);
            ledNode.dark();
          });
          this.numbers = [];
          this.animationNode.active = false;
          this.logo.active = false;
          this.node.getChildByPath('/current/bigBall').active = false;
          this.node.getChildByPath('/current/next').getComponent(Label).string = 'Current';
          this.node.getChildByPath('/current/sn').getComponent(Label).string = "SN-" + this.dataSource.currentRound;
        }

        playBounceAnimation() {
          this.animationNode.active = true;
          this.animationNode.getComponent(Animation).play();
        }

        btnAni() {
          var opacity = this.node.getChildByPath('/current/bigBall').getComponent(UIOpacity); // node.setScale(new Vec3(0.2, 0.2));
          // node.setPosition(new Vec3(0, -170, 0));

          opacity.opacity = 255;
          opacity.node.scale = new Vec3(0.2, 0.2);
          opacity.node.active = true;
          tween(opacity.node).to(0.2, {
            scale: new Vec3(1.2, 1.2)
          }).to(0.2, {
            scale: new Vec3(1, 1)
          }).then(tween(opacity).to(2, {
            opacity: 0
          })).union().start();
        }

        setDefault() {
          this.bar.active = false;
          this.logo.active = true;
          this.animationNode.active = false;
          this.node.getChildByPath('/current/bigBall').active = false;
        }

        showBigNumberAnimation(number, callback) {
          this.numbers.push(number);
          this.animationNode.active = true; // this.animationNode.getComponent(Animation).resume();

          var bigBall = this.node.getChildByPath('/current/bigBall');
          var bigBallOpacity = bigBall.getComponent(UIOpacity);
          bigBall.getComponent(_crd && BallItem === void 0 ? (_reportPossibleCrUseOfBallItem({
            error: Error()
          }), BallItem) : BallItem).setNumber(number);
          Tween.stopAllByTarget(bigBall);
          Tween.stopAllByTarget(bigBallOpacity);
          bigBallOpacity.opacity = 255;
          bigBall.scale = new Vec3(0, 0);
          bigBall.active = true;
          tween(bigBall).delay(0.5).to(0.15, {
            scale: new Vec3(1.2, 1.2)
          }).to(0.15, {
            scale: new Vec3(1, 1)
          }).call(() => {
            this.ledNodes[this.numbers.length - 1].light();

            if (callback) {
              callback();
            }
          }).delay(0.7) // .then(tween(bigBallOpacity).to(0.2, { opacity: 0 }))
          .union().call(() => {
            if (this.numbers.length == 20) {
              this.animationNode.active = false;
            } else {
              bigBallOpacity.opacity = 0;
              bigBall.active = false;
            }
          }).start();
          /*
          if (this.numbers.length == 20) {
              tween(bigBall)
                  .to(0.15, { scale: new Vec3(1.2, 1.2) })
                  .to(0.2, { scale: new Vec3(1, 1) })
                  .call(() => {
                        if (callback) {
                          callback();
                      }
                        this.tick();
                  })
                  .delay(1)
                  .union()
                  .call(() => {
                      this.animationNode.active = false;
                      // this.showBarAnimation(number);
                  })
                  .start();
          } else {
              tween(bigBall)
                  .to(0.15, { scale: new Vec3(1.2, 1.2) })
                  .to(0.2, { scale: new Vec3(1, 1) })
                  .call(() => {
                      this.ledNodes[this.numbers.length - 1].light();
                      if (callback) {
                          callback();
                      }
                  })
                  .delay(1)
                  .then(tween(bigBallOpacity).to(0.2, { opacity: 0 }))
                  .union()
                  .call(() => {
                      bigBallOpacity.opacity = 0;
                      bigBall.active = false;
                  })
                  .start();
          }
          */
        }
        /**
         * 倒计时
         * @param seconds 
         */


        countdown(seconds) {
          //流水灯显示
          if (seconds >= 20) {
            this.startFlowAnimation();
          } //  倒计时小于20秒时，开始闪烁led


          if (seconds >= 1 && seconds < 20) {
            this.tick();
          }

          if (seconds > 0 && seconds % 5 == 0) {
            (_crd && TweenUtils === void 0 ? (_reportPossibleCrUseOfTweenUtils({
              error: Error()
            }), TweenUtils) : TweenUtils).scaleAndRestore(this.logo, 1.15, 0.3, 0);
          }

          if (seconds <= 0) {
            this.ledNodes.forEach(led => {
              led.getComponent(_crd && Pearl === void 0 ? (_reportPossibleCrUseOfPearl({
                error: Error()
              }), Pearl) : Pearl).dark();
              led.node.setScale(1, 1, 1);
            });
          }

          var duration = (_crd && FormatUtils === void 0 ? (_reportPossibleCrUseOfFormatUtils({
            error: Error()
          }), FormatUtils) : FormatUtils).formatDuration(seconds);
          this.countdownLabel.string = duration;
        }
        /**
         * 闪烁led
         */


        tick() {
          this.ledNodes.forEach(node => {
            Tween.stopAllByTarget(node);
          });
          var objs = {
            value: 0
          };
          tween(objs).to(0.4, {
            value: 0
          }).call(() => {
            this.ledNodes.map(led => led.light());
          }).to(0.4, {
            value: 0
          }).call(() => {
            this.ledNodes.map(led => led.dark());
          }).union().start();
        }
        /**
         * 流水灯动画
         */


        startFlowAnimation() {
          // 计算当前要亮起的 3 个灯珠索引
          var lightsToShow = [];

          for (var i = 0; i < this.lightCount; i++) {
            lightsToShow.push((this.currentIndex + i) % this.ledNodes.length);
          } // 计算上一轮亮起的 3 个灯珠（即当前 - lightCount 到当前 -1）


          var lightsToHide = [];

          for (var _i = 0; _i < this.lightCount; _i++) {
            var index = (this.currentIndex - this.lightCount + _i + this.ledNodes.length) % this.ledNodes.length;
            lightsToHide.push(index);
          } // 逐个熄灭之前的 3 个灯珠


          lightsToHide.forEach((index, i) => {
            tween(this.ledNodes[index].node).delay(i * 0.2) // 延迟熄灭时间，与点亮同步
            .to(0.2, {
              scale: new Vec3(1, 1, 1)
            }, {
              easing: 'backIn'
            }).call(() => this.ledNodes[index].dark()).start();
          }); // 逐个点亮新的 3 个灯珠

          lightsToShow.forEach((index, i) => {
            tween(this.ledNodes[index].node).delay(i * 0.2) // 依次点亮
            .to(0.2, {
              scale: new Vec3(1, 1, 1)
            }, {
              easing: 'backOut'
            }).call(() => this.ledNodes[index].light()).start();
          }); // 更新当前索引

          this.currentIndex = (this.currentIndex + this.lightCount) % this.ledNodes.length;
        }

        onEnable() {}

        onDisable() {
          this.unscheduleAllCallbacks();
        }

        update(deltaTime) {}

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "led", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "countdownLabel", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "logo", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "bar", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "animationNode", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=46b833dd3a3ec51ee19ad67ad6fba72e08580eb7.js.map