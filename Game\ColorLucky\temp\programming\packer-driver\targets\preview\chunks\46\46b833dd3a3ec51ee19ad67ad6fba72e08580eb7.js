System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, instantiate, Label, Node, Prefab, tween, Vec3, Animation, UIOpacity, Tween, TweenUtils, FormatUtils, NumberUtils, BallItem, eventManager, Pearl, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, NumberSpawner;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfTweenUtils(extras) {
    _reporterNs.report("TweenUtils", "../framework/utils/TweenUtils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFormatUtils(extras) {
    _reporterNs.report("FormatUtils", "../framework/utils/FormatUtils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfNumberUtils(extras) {
    _reporterNs.report("NumberUtils", "../framework/utils/NumberUtils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBallItem(extras) {
    _reporterNs.report("BallItem", "./perfabs/BallItem", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeventManager(extras) {
    _reporterNs.report("eventManager", "../framework/event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPearl(extras) {
    _reporterNs.report("Pearl", "./perfabs/Pearl", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
      Label = _cc.Label;
      Node = _cc.Node;
      Prefab = _cc.Prefab;
      tween = _cc.tween;
      Vec3 = _cc.Vec3;
      Animation = _cc.Animation;
      UIOpacity = _cc.UIOpacity;
      Tween = _cc.Tween;
    }, function (_unresolved_2) {
      TweenUtils = _unresolved_2.TweenUtils;
    }, function (_unresolved_3) {
      FormatUtils = _unresolved_3.FormatUtils;
    }, function (_unresolved_4) {
      NumberUtils = _unresolved_4.NumberUtils;
    }, function (_unresolved_5) {
      BallItem = _unresolved_5.BallItem;
    }, function (_unresolved_6) {
      eventManager = _unresolved_6.eventManager;
    }, function (_unresolved_7) {
      Pearl = _unresolved_7.Pearl;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "9e017+FgvlMqrrEIhB1ODEp", "NumberSpawner", undefined);

      __checkObsolete__(['_decorator', 'Component', 'easing', 'instantiate', 'Label', 'Node', 'Prefab', 'tween', 'UITransform', 'Vec3', 'Animation', 'UIOpacity', 'Tween']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("NumberSpawner", NumberSpawner = (_dec = ccclass('NumberSpawner'), _dec2 = property(Prefab), _dec3 = property(Label), _dec4 = property(Node), _dec5 = property(Node), _dec6 = property(Node), _dec(_class = (_class2 = class NumberSpawner extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "led", _descriptor, this);

          _initializerDefineProperty(this, "countdownLabel", _descriptor2, this);

          _initializerDefineProperty(this, "logo", _descriptor3, this);

          _initializerDefineProperty(this, "bar", _descriptor4, this);

          _initializerDefineProperty(this, "animationNode", _descriptor5, this);

          this.currentIndex = 0;
          this.lightCount = 3;
          this.ledNodes = [];
          this.numbers = [];
          this.dataSource = null;
        }

        /**
         * 设置数据源并更新UI显示
         * @param data 包含游戏数据的对象，包含command、nextRound、currentRound等字段
         */
        setDataSource(data) {
          this.dataSource = data;

          if (data.command == 'ColorLuckyRecentResult') {
            this.node.getChildByPath('/current/next').getComponent(Label).string = 'Next';
            this.node.getChildByPath('/current/sn').getComponent(Label).string = "SN-" + this.dataSource.nextRound;
          } // else {
          //     this.node.getChildByPath('/current/next').getComponent(Label).string = 'Current';
          //     this.node.getChildByPath('/current/sn').getComponent(Label).string = `SN-${this.dataSource.currentRound}`;
          // }

        }
        /**
         * 组件加载时的初始化方法
         * 注册事件监听器并创建圆形排列的LED灯珠
         */


        onLoad() {
          (_crd && eventManager === void 0 ? (_reportPossibleCrUseOfeventManager({
            error: Error()
          }), eventManager) : eventManager).on("BallsPanel_Finish", this.onBallsFinish.bind(this));
          (_crd && eventManager === void 0 ? (_reportPossibleCrUseOfeventManager({
            error: Error()
          }), eventManager) : eventManager).on("BallsPanel_Start", this.onBallsStart.bind(this));
          var centerX = 0;
          var centerY = 0;
          var radius = 230;
          var numLights = 20;
          var circleNode = this.node.getChildByName('current');

          for (var i = numLights; i > 0; i--) {
            var angle = i / numLights * Math.PI * 2 + Math.PI / 2; // 将圆分成 20 等份 第一个节点在最上面

            var x = centerX + radius * Math.cos(angle);
            var y = centerY + radius * Math.sin(angle);
            var ledNode = instantiate(this.led);
            ledNode.setPosition(x, y); // 设置旋转角度，将弧度转换为度数

            var rotationAngle = angle * 180 / Math.PI - 90; // 减去90度是为了调整初始方向

            ledNode.setRotationFromEuler(0, 0, rotationAngle);
            circleNode.addChild(ledNode); // 初始化所有灯为暗态

            var ledComponent = ledNode.getComponent(_crd && Pearl === void 0 ? (_reportPossibleCrUseOfPearl({
              error: Error()
            }), Pearl) : Pearl);
            ledComponent.dark();
            this.ledNodes.push(ledComponent);
          }
        }
        /**
         * 球类游戏结束时的回调方法
         * 重置游戏状态，清空数字数组，关闭动画，显示logo
         */


        onBallsFinish() {
          this.numbers = [];
          this.animationNode.active = false;
          this.logo.active = true;
          this.node.getChildByPath('/current/led_bg').active = true;
          this.node.getChildByPath('/current/largeBall').active = false;
          this.ledNodes.forEach(ledNode => {
            Tween.stopAllByTarget(ledNode);
            ledNode.dark();
          });
          this.node.getChildByPath('/current/next').getComponent(Label).string = 'Next';
          this.node.getChildByPath('/current/sn').getComponent(Label).string = "SN-" + this.dataSource.nextRound;
        }
        /**
         * 球类游戏开始时的回调方法
         * 重置LED状态，清空数字数组，隐藏logo和动画
         */


        onBallsStart() {
          this.ledNodes.forEach(ledNode => {
            Tween.stopAllByTarget(ledNode);
            ledNode.dark();
          });
          this.numbers = [];
          this.animationNode.active = false;
          this.logo.active = false;
          this.node.getChildByPath('/current/led_bg').active = false;
          this.node.getChildByPath('/current/largeBall').active = true;
          this.node.getChildByPath('/current/next').getComponent(Label).string = 'Current';
          this.node.getChildByPath('/current/sn').getComponent(Label).string = "SN-" + this.dataSource.currentRound;
        }
        /**
         * 播放弹跳动画
         * 激活动画节点并播放动画
         */


        playBounceAnimation() {
          this.animationNode.active = true;
          this.animationNode.getComponent(Animation).play();
        }
        /**
         * 按钮动画效果
         * 显示大球的缩放和透明度动画效果
         */


        btnAni() {
          var opacity = this.node.getChildByPath('/current/bigBall').getComponent(UIOpacity); // node.setScale(new Vec3(0.2, 0.2));
          // node.setPosition(new Vec3(0, -170, 0));

          opacity.opacity = 255;
          opacity.node.scale = new Vec3(0.2, 0.2);
          opacity.node.active = true;
          tween(opacity.node).to(0.2, {
            scale: new Vec3(1.2, 1.2)
          }).to(0.2, {
            scale: new Vec3(1, 1)
          }).then(tween(opacity).to(2, {
            opacity: 0
          })).union().start();
        }
        /**
         * 设置默认状态
         * 隐藏进度条，显示logo，关闭动画和大球
         */


        setDefault() {
          this.bar.active = false;
          this.logo.active = true;
          this.animationNode.active = false;
          this.node.getChildByPath('/current/bigBall').active = false;
        }
        /**
         * 显示大数字动画 原为弹跳动画 现切换为多个数字闪动
         * @param number 要显示的数字
         * @param callback 动画完成后的回调函数
         */


        showBigNumberAnimation2(number, callback) {
          this.numbers.push(number);
          this.animationNode.active = true; // this.animationNode.getComponent(Animation).resume();

          var bigBall = this.node.getChildByPath('/current/bigBall');
          var bigBallOpacity = bigBall.getComponent(UIOpacity);
          bigBall.getComponent(_crd && BallItem === void 0 ? (_reportPossibleCrUseOfBallItem({
            error: Error()
          }), BallItem) : BallItem).setNumber(number);
          Tween.stopAllByTarget(bigBall);
          Tween.stopAllByTarget(bigBallOpacity);
          bigBallOpacity.opacity = 255;
          bigBall.scale = new Vec3(0, 0);
          bigBall.active = true;
          tween(bigBall).delay(0.5).to(0.15, {
            scale: new Vec3(1.2, 1.2)
          }).to(0.15, {
            scale: new Vec3(1, 1)
          }).call(() => {
            this.ledNodes[this.numbers.length - 1].light();

            if (callback) {
              callback();
            }
          }).delay(0.7) // .then(tween(bigBallOpacity).to(0.2, { opacity: 0 }))
          .union().call(() => {
            if (this.numbers.length == 20) {
              this.animationNode.active = false;
            } else {
              bigBallOpacity.opacity = 0;
              bigBall.active = false;
            }
          }).start();
          /*
          if (this.numbers.length == 20) {
              tween(bigBall)
                  .to(0.15, { scale: new Vec3(1.2, 1.2) })
                  .to(0.2, { scale: new Vec3(1, 1) })
                  .call(() => {
                        if (callback) {
                          callback();
                      }
                        this.tick();
                  })
                  .delay(1)
                  .union()
                  .call(() => {
                      this.animationNode.active = false;
                      // this.showBarAnimation(number);
                  })
                  .start();
          } else {
              tween(bigBall)
                  .to(0.15, { scale: new Vec3(1.2, 1.2) })
                  .to(0.2, { scale: new Vec3(1, 1) })
                  .call(() => {
                      this.ledNodes[this.numbers.length - 1].light();
                      if (callback) {
                          callback();
                      }
                  })
                  .delay(1)
                  .then(tween(bigBallOpacity).to(0.2, { opacity: 0 }))
                  .union()
                  .call(() => {
                      bigBallOpacity.opacity = 0;
                      bigBall.active = false;
                  })
                  .start();
          }
          */
        }
        /**
         * 显示大数字动画
         * @param number 要显示的数字
         * @param callback 动画完成后的回调函数
         */


        showBigNumberAnimation(number, callback) {
          this.numbers.push(number); // this.animationNode.active = true;

          var bigBall = this.node.getChildByPath('/current/largeBall');
          var bigBallOpacity = bigBall.getComponent(UIOpacity); // 停止所有正在进行的动画

          Tween.stopAllByTarget(bigBall);
          Tween.stopAllByTarget(bigBallOpacity); //随机5个数字

          var randomNumbers = (_crd && NumberUtils === void 0 ? (_reportPossibleCrUseOfNumberUtils({
            error: Error()
          }), NumberUtils) : NumberUtils).getRandomNumbers(4);
          randomNumbers.push(number);
          randomNumbers.forEach((num, idx) => {
            tween(bigBall).delay(0.15 * idx).call(() => {
              bigBall.getComponent(_crd && BallItem === void 0 ? (_reportPossibleCrUseOfBallItem({
                error: Error()
              }), BallItem) : BallItem).setNumber(num);

              if (idx == randomNumbers.length - 1) {
                this.ledNodes[this.numbers.length - 1].light();

                if (callback) {
                  callback();
                }
              }
            }).start();
          }); // // 随机闪动5次的动画序列
          // this.playRandomFlashAnimation(bigBall, 5, 0.3, () => {
          //     // 闪动完成后显示最终数字
          //     // 最终数字的缩放动画
          //     tween(bigBall)
          //         .to(0.15, { scale: new Vec3(1.2, 1.2, 1.2) })
          //         .to(0.15, { scale: new Vec3(1, 1, 1) })
          //         .call(() => {
          //             // 点亮对应的LED灯珠
          //             this.ledNodes[this.numbers.length - 1].light();
          //             if (callback) {
          //                 callback();
          //             }
          //         })
          //         .delay(0.7)
          //         .call(() => {
          //             if (this.numbers.length == 20) {
          //                 this.animationNode.active = false;
          //             } else {
          //                 bigBallOpacity.opacity = 0;
          //                 bigBall.active = false;
          //             }
          //         })
          //         .start();
          // });
        }
        /**
         * 播放随机闪动动画
         * @param targetNode 目标节点
         * @param flashCount 闪动次数
         * @param flashDuration 每次闪动的持续时间
         * @param onComplete 完成回调
         */


        playRandomFlashAnimation(targetNode, flashCount, flashDuration, onComplete) {
          var currentFlash = 0;

          var playNextFlash = () => {
            if (currentFlash >= flashCount) {
              if (onComplete) {
                onComplete();
              }

              return;
            } // 生成1-20之间的随机数字


            var randomNumber = Math.floor(Math.random() * 20) + 1;
            targetNode.getComponent(_crd && BallItem === void 0 ? (_reportPossibleCrUseOfBallItem({
              error: Error()
            }), BallItem) : BallItem).setNumber(randomNumber); // 闪动效果：快速缩放

            tween(targetNode).to(flashDuration * 0.3, {
              scale: new Vec3(1.1, 1.1, 1.1)
            }).to(flashDuration * 0.4, {
              scale: new Vec3(0.9, 0.9, 0.9)
            }).to(flashDuration * 0.3, {
              scale: new Vec3(1, 1, 1)
            }).call(() => {
              currentFlash++; // 延迟一小段时间后播放下一次闪动

              this.scheduleOnce(() => {
                playNextFlash();
              }, 0.1);
            }).start();
          }; // 开始第一次闪动


          playNextFlash();
        }
        /**
         * 倒计时功能
         * @param seconds 倒计时秒数
         */


        countdown(seconds) {
          //流水灯显示
          if (seconds >= 20) {
            this.startFlowAnimation();
          } //  倒计时小于20秒时，开始闪烁led


          if (seconds >= 1 && seconds < 20) {
            this.tick();
          }

          if (seconds > 0 && seconds % 5 == 0) {
            (_crd && TweenUtils === void 0 ? (_reportPossibleCrUseOfTweenUtils({
              error: Error()
            }), TweenUtils) : TweenUtils).scaleAndRestore(this.logo, 1.15, 0.3, 0);
          }

          if (seconds <= 0) {
            this.ledNodes.forEach(led => {
              led.getComponent(_crd && Pearl === void 0 ? (_reportPossibleCrUseOfPearl({
                error: Error()
              }), Pearl) : Pearl).dark();
              led.node.setScale(1, 1, 1);
            });
          }

          var duration = (_crd && FormatUtils === void 0 ? (_reportPossibleCrUseOfFormatUtils({
            error: Error()
          }), FormatUtils) : FormatUtils).formatDuration(seconds);
          this.countdownLabel.string = duration;
        }
        /**
         * LED灯珠闪烁效果
         * 让所有LED灯珠同时闪烁一次
         */


        tick() {
          this.ledNodes.forEach(node => {
            Tween.stopAllByTarget(node);
          });
          var objs = {
            value: 0
          };
          tween(objs).to(0.4, {
            value: 0
          }).call(() => {
            this.ledNodes.map(led => led.light());
          }).to(0.4, {
            value: 0
          }).call(() => {
            this.ledNodes.map(led => led.dark());
          }).union().start();
        }
        /**
         * 流水灯动画效果
         * 创建连续的LED灯珠流动效果，每次点亮3个连续的灯珠
         */


        startFlowAnimation() {
          // 计算当前要亮起的 3 个灯珠索引
          var lightsToShow = [];

          for (var i = 0; i < this.lightCount; i++) {
            lightsToShow.push((this.currentIndex + i) % this.ledNodes.length);
          } // 计算上一轮亮起的 3 个灯珠（即当前 - lightCount 到当前 -1）


          var lightsToHide = [];

          for (var _i = 0; _i < this.lightCount; _i++) {
            var index = (this.currentIndex - this.lightCount + _i + this.ledNodes.length) % this.ledNodes.length;
            lightsToHide.push(index);
          } // 逐个熄灭之前的 3 个灯珠


          lightsToHide.forEach((index, i) => {
            tween(this.ledNodes[index].node).delay(i * 0.2) // 延迟熄灭时间，与点亮同步
            .to(0.2, {
              scale: new Vec3(1, 1, 1)
            }, {
              easing: 'backIn'
            }).call(() => this.ledNodes[index].dark()).start();
          }); // 逐个点亮新的 3 个灯珠

          lightsToShow.forEach((index, i) => {
            tween(this.ledNodes[index].node).delay(i * 0.2) // 依次点亮
            .to(0.2, {
              scale: new Vec3(1, 1, 1)
            }, {
              easing: 'backOut'
            }).call(() => this.ledNodes[index].light()).start();
          }); // 更新当前索引

          this.currentIndex = (this.currentIndex + this.lightCount) % this.ledNodes.length;
        }
        /**
         * 组件启用时的回调方法
         * 当前为空实现，可在此添加启用时的逻辑
         */


        onEnable() {}
        /**
         * 组件禁用时的回调方法
         * 取消所有定时器回调
         */


        onDisable() {
          this.unscheduleAllCallbacks();
        }
        /**
         * 每帧更新方法
         * @param deltaTime 帧间隔时间
         * 当前为空实现，可在此添加每帧需要执行的逻辑
         */


        update(deltaTime) {}

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "led", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "countdownLabel", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "logo", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "bar", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "animationNode", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=46b833dd3a3ec51ee19ad67ad6fba72e08580eb7.js.map