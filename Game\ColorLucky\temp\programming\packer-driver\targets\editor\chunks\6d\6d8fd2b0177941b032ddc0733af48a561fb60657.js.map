{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAAqK,uCAArK,EAAsU,uCAAtU,EAAie,uCAAje,EAAqnB,uCAArnB,EAA8tB,uCAA9tB,EAAk0B,uCAAl0B,EAA06B,uCAA16B,EAA+gC,uCAA/gC,EAAunC,uCAAvnC,EAA+tC,wCAA/tC,EAAs0C,wCAAt0C,EAAq6C,wCAAr6C,EAAmgD,wCAAngD,EAAqmD,wCAArmD,EAA0sD,wCAA1sD,EAA+yD,wCAA/yD,EAAm5D,wCAAn5D,EAA0/D,wCAA1/D,EAA+lE,wCAA/lE,EAA+rE,wCAA/rE,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.4/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.4/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.4/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.4/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/event/EventManager.ts\"), () => import(\"file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/logger/logger.ts\"), () => import(\"file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/network/websocket.ts\"), () => import(\"file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/ui/PageAdapter.ts\"), () => import(\"file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/FormatUtils.ts\"), () => import(\"file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/NumberUtils.ts\"), () => import(\"file:///E:/Workspace/magic-match/Game/ColorLucky/assets/framework/utils/TweenUtils.ts\"), () => import(\"file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/ColorLucky.ts\"), () => import(\"file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/Countdown.ts\"), () => import(\"file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/NumberSpawner.ts\"), () => import(\"file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/BallsPanel.ts\"), () => import(\"file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/ColorPanel.ts\"), () => import(\"file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/MainPanel.ts\"), () => import(\"file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/panel/SuggestPanel.ts\"), () => import(\"file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/BallItem.ts\"), () => import(\"file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/Led.ts\"), () => import(\"file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/Pearl.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}