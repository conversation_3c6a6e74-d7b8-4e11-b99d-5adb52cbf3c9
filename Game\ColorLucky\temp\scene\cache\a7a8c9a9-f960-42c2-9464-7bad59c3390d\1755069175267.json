"[\n  {\n    \"__type__\": \"cc.SceneAsset\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_native\": \"\",\n    \"scene\": {\n      \"__id__\": 1\n    }\n  },\n  {\n    \"__type__\": \"cc.Scene\",\n    \"_name\": \"ColorLucky\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": null,\n    \"_children\": [\n      {\n        \"__id__\": 2\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": {\n      \"__id__\": 394\n    },\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"autoReleaseAssets\": false,\n    \"_globals\": {\n      \"__id__\": 397\n    },\n    \"_id\": \"bb178f2a-b8ec-40c5-b625-fa77e0041139\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Canvas\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 1\n    },\n    \"_children\": [\n      {\n        \"__id__\": 3\n      },\n      {\n        \"__id__\": 5\n      },\n      {\n        \"__id__\": 8\n      },\n      {\n        \"__id__\": 21\n      },\n      {\n        \"__id__\": 309\n      },\n      {\n        \"__id__\": 324\n      },\n      {\n        \"__id__\": 349\n      },\n      {\n        \"__id__\": 362\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 389\n      },\n      {\n        \"__id__\": 390\n      },\n      {\n        \"__id__\": 391\n      },\n      {\n        \"__id__\": 392\n      },\n      {\n        \"__id__\": 393\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 640,\n      \"y\": 360.00000000000006,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"beI88Z2HpFELqR4T5EMHpg\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Camera\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 4\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 1000\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"ebFwiq8gBFaYpqYbdoDODe\"\n  },\n  {\n    \"__type__\": \"cc.Camera\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 3\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_projection\": 0,\n    \"_priority\": 0,\n    \"_fov\": 45,\n    \"_fovAxis\": 0,\n    \"_orthoHeight\": 381.3995485327314,\n    \"_near\": 0,\n    \"_far\": 2000,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_depth\": 1,\n    \"_stencil\": 0,\n    \"_clearFlags\": 7,\n    \"_rect\": {\n      \"__type__\": \"cc.Rect\",\n      \"x\": 0,\n      \"y\": 0,\n      \"width\": 1,\n      \"height\": 1\n    },\n    \"_aperture\": 19,\n    \"_shutter\": 7,\n    \"_iso\": 0,\n    \"_screenScale\": 1,\n    \"_visibility\": 1108344832,\n    \"_targetTexture\": null,\n    \"_postProcess\": null,\n    \"_usePostProcess\": false,\n    \"_cameraType\": -1,\n    \"_trackingType\": 0,\n    \"_id\": \"63WIch3o5BEYRlXzTT0oWc\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"bg\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 6\n      },\n      {\n        \"__id__\": 7\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.667,\n      \"y\": 0.667,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"8el+4Iw8BGKaBTrEyWVj38\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 5\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 1920,\n      \"height\": 1080\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"43QlHJVDNAOr88GwJHVBDH\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 5\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"786481c0-5050-43cb-8e5a-07898d8c07da@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 1,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"2ena4Vf1hOFZCscWUwjRBQ\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_prefab\": {\n      \"__id__\": 9\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 8\n    },\n    \"asset\": {\n      \"__uuid__\": \"a4998c87-66f0-436d-8378-a44be9840e96\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"83Bahp1ndLhJBe92Qiplp5\",\n    \"instance\": {\n      \"__id__\": 10\n    },\n    \"targetOverrides\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"2bBcLpI/tF4bk3pKSGUwjj\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 11\n      },\n      {\n        \"__id__\": 13\n      },\n      {\n        \"__id__\": 15\n      },\n      {\n        \"__id__\": 17\n      },\n      {\n        \"__id__\": 19\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 12\n    },\n    \"propertyPath\": [\n      \"_name\"\n    ],\n    \"value\": \"mainPanel\"\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"83Bahp1ndLhJBe92Qiplp5\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 14\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"83Bahp1ndLhJBe92Qiplp5\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 16\n    },\n    \"propertyPath\": [\n      \"_lrot\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"83Bahp1ndLhJBe92Qiplp5\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 18\n    },\n    \"propertyPath\": [\n      \"_euler\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"83Bahp1ndLhJBe92Qiplp5\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 20\n    },\n    \"propertyPath\": [\n      \"_active\"\n    ],\n    \"value\": false\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"83Bahp1ndLhJBe92Qiplp5\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"instruction\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 22\n      },\n      {\n        \"__id__\": 25\n      },\n      {\n        \"__id__\": 34\n      },\n      {\n        \"__id__\": 304\n      }\n    ],\n    \"_active\": false,\n    \"_components\": [\n      {\n        \"__id__\": 307\n      },\n      {\n        \"__id__\": 308\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -5.684341886080802e-14,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.667,\n      \"y\": 0.667,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"53D6nAE0lPmo5MZlUKTziP\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"keno\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 21\n    },\n    \"_children\": [],\n    \"_active\": false,\n    \"_components\": [\n      {\n        \"__id__\": 23\n      },\n      {\n        \"__id__\": 24\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -138.151,\n      \"y\": 382.073,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"8buLo7o+pBX5t8PDch5ig9\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 22\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 356,\n      \"height\": 166\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"2dqQ8eY3RIW6Bkcm82ioBX\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 22\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"fc247c7f-1ab1-4a02-b4a7-d6085b5b7b5d@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 1,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"2agnzieBpBkIzoe5YrOGaD\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"title\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 21\n    },\n    \"_children\": [\n      {\n        \"__id__\": 26\n      },\n      {\n        \"__id__\": 29\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 32\n      },\n      {\n        \"__id__\": 33\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 498.496,\n      \"y\": 377.096,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"84N8UM/+FDbLAzC8hqyyf1\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"3\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 25\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 27\n      },\n      {\n        \"__id__\": 28\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -205,\n      \"y\": 48,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.5,\n      \"y\": 0.5,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"acy5hZMV1M4rfkI6+mkI3U\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 26\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 71,\n      \"height\": 91\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"68yMkrZQBJ9oejJyGB/JBo\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 26\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"24b8ded8-0c94-4f17-9788-6fbc1c74ba14@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 1,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"46Ay21UdlOVrlk4AsKICys\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"8\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 25\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 30\n      },\n      {\n        \"__id__\": 31\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -125,\n      \"y\": 48,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.5,\n      \"y\": 0.5,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"d2cDTEPwdOB6GPaG9mDMAS\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 29\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 96,\n      \"height\": 112\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"b68kgQEw5PpYx9ZNIVxhYn\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 29\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"14e20800-59e8-489c-a9a3-372ee7f9cd4a@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 1,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"6aJEVMcTdHOJCdtCgM5fam\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 25\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 666.404296875,\n      \"height\": 163\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"470QAP4i9KzL+wjEd4MWjp\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 25\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"Select     to     numbers betwoen 1 and 80\\nHit at least 3 numbers to win,\\nhit more and win more!\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 36,\n    \"_fontSize\": 36,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 50,\n    \"_overflow\": 0,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 40,\n      \"g\": 22,\n      \"b\": 104,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 2,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"6ctIgx63FDy6Y1ubZOgZ5A\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 21\n    },\n    \"_children\": [\n      {\n        \"__id__\": 35\n      },\n      {\n        \"__id__\": 104\n      },\n      {\n        \"__id__\": 124\n      },\n      {\n        \"__id__\": 182\n      },\n      {\n        \"__id__\": 213\n      },\n      {\n        \"__id__\": 263\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 302\n      },\n      {\n        \"__id__\": 303\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 256.388,\n      \"y\": -93.744,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"06cC8yh+1CWZMbwIUGJSyD\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"8\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 34\n    },\n    \"_children\": [\n      {\n        \"__id__\": 36\n      },\n      {\n        \"__id__\": 45\n      },\n      {\n        \"__id__\": 53\n      },\n      {\n        \"__id__\": 64\n      },\n      {\n        \"__id__\": 72\n      },\n      {\n        \"__id__\": 83\n      },\n      {\n        \"__id__\": 91\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 102\n      },\n      {\n        \"__id__\": 103\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -418,\n      \"y\": 95,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"47D66ROXFPSZfS/DE0FN02\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 35\n    },\n    \"_children\": [\n      {\n        \"__id__\": 37\n      },\n      {\n        \"__id__\": 40\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 43\n      },\n      {\n        \"__id__\": 44\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.51220703125,\n      \"y\": 167.2,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"f8ZU6A8pxBzZ0v/3VdOhuJ\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"line\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 36\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 38\n      },\n      {\n        \"__id__\": 39\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.69,\n      \"y\": -33.781,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"ffzsOEyvhF5auEFV3GzIJw\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 37\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 2\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"23LzMbYrhGoaF2MvHNJns1\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 37\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"176fKhY0lP35B4l1Xy2Tpk\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"8\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 36\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 41\n      },\n      {\n        \"__id__\": 42\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 23,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"6esROYuUxD0YF7VAC45L+Q\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 40\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 96,\n      \"height\": 112\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"adTaMQI55G/KXh+6rftFdD\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 40\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"14e20800-59e8-489c-a9a3-372ee7f9cd4a@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 1,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"a9kmtxQzZH1qYe/2kFWn+e\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 36\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 365.0244140625,\n      \"height\": 75.6\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"6eKuHmYE1JsKUGBCt57BPa\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 36\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"HIT                               WIN\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 30,\n    \"_fontSize\": 30,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 60,\n    \"_overflow\": 0,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"78QJfvhR9PJZNHLq2DnRQG\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 35\n    },\n    \"_children\": [\n      {\n        \"__id__\": 46\n      },\n      {\n        \"__id__\": 49\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 52\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 101.4,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"e74hdCejVLMKFdEie7VRWA\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 45\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 47\n      },\n      {\n        \"__id__\": 48\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"dfxP/cn99Ndr5ntTsmI52m\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 46\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"39x8vmom5MQb6ibeS/t1dr\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 46\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"8\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"53g8zxGv1ABbeAkqdN30Vg\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 45\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 50\n      },\n      {\n        \"__id__\": 51\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"68T0vgPixK1oHTgwrz15TO\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 49\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"1eqLVGmmVArYHGipxfLnM8\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 49\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"50 000x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"b2rM+MZMlA4o2mY+3z2Bwj\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 45\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"e98miVHM9BSKZ9qV+cgYiG\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 35\n    },\n    \"_children\": [\n      {\n        \"__id__\": 54\n      },\n      {\n        \"__id__\": 57\n      },\n      {\n        \"__id__\": 60\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 63\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 45.400000000000006,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"b9YVpTkedLSqNVgds/l7UH\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"SpriteSplash\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 53\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 55\n      },\n      {\n        \"__id__\": 56\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"331J6f8eBLGohHHuRFyrE/\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 54\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"31iR+ZlhFCbK0dAfhmn0NZ\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 54\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 30\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"b4lpMNtWtP8Ktuwb/fnhXo\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 53\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 58\n      },\n      {\n        \"__id__\": 59\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"e3lxuiTW5D66VBSQqRXYyo\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 57\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"a5VPpNOfND3qs302MtyKU0\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 57\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"7\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"edgj6LZndI14EbXMJ4J2fL\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 53\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 61\n      },\n      {\n        \"__id__\": 62\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"25Q6cmsWFAyasI7iNJeO8W\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 60\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"24yJZhMvVIEKb2B1fkhygQ\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 60\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"800x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"27HI0slodJp70/xXC18Ksp\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 53\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"95mNDKU05IFo3n72n0aI2Q\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node-002\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 35\n    },\n    \"_children\": [\n      {\n        \"__id__\": 65\n      },\n      {\n        \"__id__\": 68\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 71\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -10.599999999999994,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"99cyEaIYlC2IL4G2cGO71G\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 64\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 66\n      },\n      {\n        \"__id__\": 67\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"26/jcqMIxIuZKZJp7CLSVb\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 65\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"d7pc3ydLBE8ae+EnwnQTaH\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 65\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"6\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"5aOl6bc8BFx4ioFUUhgk3D\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 64\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 69\n      },\n      {\n        \"__id__\": 70\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"c4uEV87NNLzqHN8LXVwugb\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 68\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"b7pXoldrZKdZLQ/YNHjB7A\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 68\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"50x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"bbH/kozUNFj488sjwdqi2b\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 64\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"a97A4lI/VLs6jHTxCvCNKX\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node-003\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 35\n    },\n    \"_children\": [\n      {\n        \"__id__\": 73\n      },\n      {\n        \"__id__\": 76\n      },\n      {\n        \"__id__\": 79\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 82\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -66.60000000000002,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"2cfSpx+59KIbJ753/0dm/M\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"SpriteSplash\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 72\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 74\n      },\n      {\n        \"__id__\": 75\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"cbqHlgWBdHVIFcp2qgOyQk\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 73\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"6d7yQpo7FI1pHpV5IO9GEp\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 73\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 30\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"50/oL1f49NnK7vEAr6INyZ\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 72\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 77\n      },\n      {\n        \"__id__\": 78\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"eebLTjwA5LyqXKUq+7ezqy\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 76\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"83Xb4Bqx1BIrmfq4HHSHD4\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 76\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"5\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"f18YnpnhxLyreS5cuVGrmu\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 72\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 80\n      },\n      {\n        \"__id__\": 81\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"c8p0ySXh1LKZSgvED6TM0F\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 79\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"34chiJjShPd6qTTFZcZGF+\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 79\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"7x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"eej1VQ/3dLkrWkV5rUxD2i\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 72\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"17wlK9YS5D3q3SZvkrDy1y\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node-004\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 35\n    },\n    \"_children\": [\n      {\n        \"__id__\": 84\n      },\n      {\n        \"__id__\": 87\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 90\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -122.60000000000002,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"23wwKnd0tHt5wHj3SewgB7\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 83\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 85\n      },\n      {\n        \"__id__\": 86\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"d9IsiQxQ5AeoFg40KhHzYz\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 84\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"e410ir5KpGpov4+TAnq+1U\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 84\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"4\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"5dx5XPWZ5MWZb6xr0JoPom\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 83\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 88\n      },\n      {\n        \"__id__\": 89\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"27Ud0HjRBG4YE9I2bMs0Ty\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 87\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"efyLG8t9BCuLZ87FawA4Wd\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 87\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"2x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"6ah2DhiN5Ig6LzhxkM6i7+\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 83\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"e0hIJflv9BmYrM9HfSvsGl\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node-005\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 35\n    },\n    \"_children\": [\n      {\n        \"__id__\": 92\n      },\n      {\n        \"__id__\": 95\n      },\n      {\n        \"__id__\": 98\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 101\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -178.60000000000002,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"f7YMZxcKJHbJB0phmUp1aV\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"SpriteSplash\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 91\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 93\n      },\n      {\n        \"__id__\": 94\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"3501CF/F9MS64gQC+5EqP6\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 92\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"52PVn6OCtP7IJ0Ms5dfS/4\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 92\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 30\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"d0mnMO+IdEDo5fH+Y0lyp/\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 91\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 96\n      },\n      {\n        \"__id__\": 97\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"e01TTf0LhM/p+gBVdC64W5\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 95\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"15IfC45dRGZ7DgbFyaS1XG\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 95\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"3\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"c2XonsJJ1K/4JI5RFP6DI7\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 91\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 99\n      },\n      {\n        \"__id__\": 100\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"ad66ORX6FPE4Xp+vbb9L73\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 98\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"abspWy2qdM2KNPROswVSIc\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 98\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"1x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"87x6Thj0xLGY+xHQVKsPCH\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 91\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"2cW4Sfu/1MIaDYGVJSgQ1W\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 35\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 410\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"bfIgfmczNPlIrcvbSzfgq7\"\n  },\n  {\n    \"__type__\": \"cc.Layout\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 35\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_resizeMode\": 0,\n    \"_layoutType\": 3,\n    \"_cellSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 40,\n      \"height\": 40\n    },\n    \"_startAxis\": 0,\n    \"_paddingLeft\": 0,\n    \"_paddingRight\": 0,\n    \"_paddingTop\": 0,\n    \"_paddingBottom\": 0,\n    \"_spacingX\": 0,\n    \"_spacingY\": 0,\n    \"_verticalDirection\": 1,\n    \"_horizontalDirection\": 0,\n    \"_constraint\": 0,\n    \"_constraintNum\": 2,\n    \"_affectedByScale\": false,\n    \"_isAlign\": false,\n    \"_id\": \"750HKH7UJI5KGD4bNQB3mV\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"3\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 34\n    },\n    \"_children\": [\n      {\n        \"__id__\": 105\n      },\n      {\n        \"__id__\": 114\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 122\n      },\n      {\n        \"__id__\": 123\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -418,\n      \"y\": -222.5,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"5dzWQIP2RH3bsKSZ5ASjMN\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 104\n    },\n    \"_children\": [\n      {\n        \"__id__\": 106\n      },\n      {\n        \"__id__\": 109\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 112\n      },\n      {\n        \"__id__\": 113\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.51220703125,\n      \"y\": 29.700000000000003,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"28QwAen7hFGKrcc6HYDLyn\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"line\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 105\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 107\n      },\n      {\n        \"__id__\": 108\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.69,\n      \"y\": -33.781,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"c6M/6AmtJBgqknTpxp+6Jh\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 106\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 2\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"37yjGENXtHRrH5FCrCHG8r\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 106\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"11Xe5LySJBUpYveScIF2bz\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"3\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 105\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 110\n      },\n      {\n        \"__id__\": 111\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 23,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"9duejrGtdG+YTA0Mh2lE2v\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 109\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 71,\n      \"height\": 91\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"d0VrUK1B9IYIlYZnY+2FZO\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 109\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"24b8ded8-0c94-4f17-9788-6fbc1c74ba14@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 1,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"2f7U+2LOtJPqOd4oSmdvhi\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 105\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 365.0244140625,\n      \"height\": 75.6\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"a7l+sullFAhLgdm+uVzJbc\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 105\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"HIT                               WIN\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 30,\n    \"_fontSize\": 30,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 60,\n    \"_overflow\": 0,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"55swAmO4RGCbdNykOX+/xN\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 104\n    },\n    \"_children\": [\n      {\n        \"__id__\": 115\n      },\n      {\n        \"__id__\": 118\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 121\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -36.099999999999994,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"4bn7Wb3JhP0oG7IYSQY10s\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 114\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 116\n      },\n      {\n        \"__id__\": 117\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"deBwysCEBMIq/mAD+JwSsr\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 115\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"61X9sw865Ih6cOzE5Z5oJl\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 115\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"3\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"faZ7HWcwFJTYg34Lv7HTUg\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 114\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 119\n      },\n      {\n        \"__id__\": 120\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"ec5TnfsshBwa4+Z0lC8eJw\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 118\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"94D/lBlQlPkqgDSJ3p+97j\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 118\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"70x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"856DNzGr1J1aTiQfcclzHo\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 114\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"c3vRvVrrVIOodNB5I+uREE\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 104\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 135\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"42dafQJilGdakB+4MGvKB0\"\n  },\n  {\n    \"__type__\": \"cc.Layout\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 104\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_resizeMode\": 0,\n    \"_layoutType\": 3,\n    \"_cellSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 40,\n      \"height\": 40\n    },\n    \"_startAxis\": 0,\n    \"_paddingLeft\": 0,\n    \"_paddingRight\": 0,\n    \"_paddingTop\": 0,\n    \"_paddingBottom\": 0,\n    \"_spacingX\": 0,\n    \"_spacingY\": 0,\n    \"_verticalDirection\": 1,\n    \"_horizontalDirection\": 0,\n    \"_constraint\": 0,\n    \"_constraintNum\": 2,\n    \"_affectedByScale\": false,\n    \"_isAlign\": false,\n    \"_id\": \"19CuDTDkFPWLwhXGca+G5n\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"7\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 34\n    },\n    \"_children\": [\n      {\n        \"__id__\": 125\n      },\n      {\n        \"__id__\": 134\n      },\n      {\n        \"__id__\": 142\n      },\n      {\n        \"__id__\": 153\n      },\n      {\n        \"__id__\": 161\n      },\n      {\n        \"__id__\": 172\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 180\n      },\n      {\n        \"__id__\": 181\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -9,\n      \"y\": 125,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"30zvASkrlNRZdc4Z0J5TzD\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 124\n    },\n    \"_children\": [\n      {\n        \"__id__\": 126\n      },\n      {\n        \"__id__\": 129\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 132\n      },\n      {\n        \"__id__\": 133\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.51220703125,\n      \"y\": 137.2,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"faYG99gRhCwomTAZlwgNWn\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"line\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 125\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 127\n      },\n      {\n        \"__id__\": 128\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.69,\n      \"y\": -33.781,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"158iTpu5ZOHbxUcuXdXcbR\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 126\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 2\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"1071rgscxA5Lltq2n3SBeK\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 126\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"b0j7I3Mb5Dard4Rpx8EST5\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"8\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 125\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 130\n      },\n      {\n        \"__id__\": 131\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 23,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"4a34GDuylHJoAVkIB9YDkY\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 129\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 74,\n      \"height\": 92\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"c8qErPuapO/5AIDOKYUwpm\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 129\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"27480af6-83d9-492a-8aeb-f5852244f986@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 1,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"9dn87d881EjqCN6xWX6bJT\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 125\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 365.0244140625,\n      \"height\": 75.6\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"5alF0DzdxG9aJGkqwD0zKl\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 125\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"HIT                               WIN\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 30,\n    \"_fontSize\": 30,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 60,\n    \"_overflow\": 0,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"08/qaVge1ICJ8jzfF7CIJo\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 124\n    },\n    \"_children\": [\n      {\n        \"__id__\": 135\n      },\n      {\n        \"__id__\": 138\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 141\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 71.4,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"27NeXbTBZNWYvO/P0JIAtK\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 134\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 136\n      },\n      {\n        \"__id__\": 137\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"09mkofNGRDp5QKRr3xo6mu\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 135\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"67kpdfAUBGW7ajJp2J9C7w\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 135\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"7\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"81b4pLqb5L85XMKDKUHZpV\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 134\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 139\n      },\n      {\n        \"__id__\": 140\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"a8GX3e95pAf4S1W3YRKapa\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 138\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"8ad+Iw0s5LiK+WCq/FaqIx\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 138\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"6 000x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"98jOkUqPpMT7gXDXjR3nwU\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 134\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"e8/d45ywxLT5bo99MnnuMs\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 124\n    },\n    \"_children\": [\n      {\n        \"__id__\": 143\n      },\n      {\n        \"__id__\": 146\n      },\n      {\n        \"__id__\": 149\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 152\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 15.400000000000006,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"69B3pnLGpOLo+e43dgcXON\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"SpriteSplash\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 142\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 144\n      },\n      {\n        \"__id__\": 145\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"1eAU4N1a9PcbHNFelOsKKg\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 143\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"efAeih6+1BloX6xlBQpGo+\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 143\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 30\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"56nXpkpO1FHY4L38rLDZK2\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 142\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 147\n      },\n      {\n        \"__id__\": 148\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"a6NiUpKTVNbZxd1Xq5E6vj\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 146\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"f2XqvMRIxA/b1CLVm+YVXk\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 146\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"6\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"aalnbxWgZBw4AE6KlGPhaj\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 142\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 150\n      },\n      {\n        \"__id__\": 151\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"1043qyd2BOuKEYq6SVw/ES\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 149\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"81G2fZCfFJ1Lti+2SJRoQ2\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 149\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"200x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"7bgsZZD5BOhZf6IBvAyBJU\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 142\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"36SZhoczNMs4C7VZ+GAjsR\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node-002\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 124\n    },\n    \"_children\": [\n      {\n        \"__id__\": 154\n      },\n      {\n        \"__id__\": 157\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 160\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -40.599999999999994,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"d6dCc4OaxETp7aS4IeQl5w\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 153\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 155\n      },\n      {\n        \"__id__\": 156\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"cf4t8YKX1AHokZCS0C2JnJ\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 154\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"88cK7FhMlO+5ePTNl2sOoV\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 154\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"5\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"8biBeS51xJfJMyB6Zzja81\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 153\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 158\n      },\n      {\n        \"__id__\": 159\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"a0BfiUjrFH6LA+cabuFtE9\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 157\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"95Exx9HpdFrIYZwqBht68F\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 157\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"40x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"17xY2YeHlFmIE2pnOwUd8W\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 153\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"d8LzsVTNRBIquv80Acbh8J\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node-003\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 124\n    },\n    \"_children\": [\n      {\n        \"__id__\": 162\n      },\n      {\n        \"__id__\": 165\n      },\n      {\n        \"__id__\": 168\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 171\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -96.60000000000002,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"8dRqTH2uFIvr4cBk2Oe7F4\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"SpriteSplash\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 161\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 163\n      },\n      {\n        \"__id__\": 164\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"f3jZwbZmZBCqy72KsTmP+2\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 162\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"91NJxsPG1OTqvSe8WuxWOv\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 162\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 30\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"eeNftqvGZGHq4y7h/0RvWT\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 161\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 166\n      },\n      {\n        \"__id__\": 167\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"bfDZuZMGhDN5FjFR/3R+zM\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 165\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"57NoMwW6VABL+of0nyOtRo\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 165\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"4\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"abN0BaiZZAh4S+F60pzBQP\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 161\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 169\n      },\n      {\n        \"__id__\": 170\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"cbGkvKYsVIioKYQBVeGY7S\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 168\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"44JBI8GvNMjbBDBwOZEokl\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 168\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"3x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"89oA+vxwlM7LWtHTlP4YzW\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 161\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"7f0mChLLFLQoXtKErE+O5J\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node-004\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 124\n    },\n    \"_children\": [\n      {\n        \"__id__\": 173\n      },\n      {\n        \"__id__\": 176\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 179\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -152.60000000000002,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"52PuYVhXZDbIu6s4jMJteO\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 172\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 174\n      },\n      {\n        \"__id__\": 175\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"4a7lKTfehLz7ASTNRksOUp\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 173\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"c0cXtmWldKMZRWk6UjMlZ8\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 173\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"3\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"0duqxjRZVOJoJeA+JLqO8z\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 172\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 177\n      },\n      {\n        \"__id__\": 178\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"b4mSozEfJIGbu9DWNJoZIK\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 176\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"7fx8jBiQZCEKmPbIx6nUB1\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 176\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"1x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"83ofH8WAZPPaaGD9ZzJA0o\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 172\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"47yotUHRNJb5PJ0wkRk9ab\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 124\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 350\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"5ayYlDPEBLSIer3QIBgKBG\"\n  },\n  {\n    \"__type__\": \"cc.Layout\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 124\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_resizeMode\": 0,\n    \"_layoutType\": 3,\n    \"_cellSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 40,\n      \"height\": 40\n    },\n    \"_startAxis\": 0,\n    \"_paddingLeft\": 0,\n    \"_paddingRight\": 0,\n    \"_paddingTop\": 0,\n    \"_paddingBottom\": 0,\n    \"_spacingX\": 0,\n    \"_spacingY\": 0,\n    \"_verticalDirection\": 1,\n    \"_horizontalDirection\": 0,\n    \"_constraint\": 0,\n    \"_constraintNum\": 2,\n    \"_affectedByScale\": false,\n    \"_isAlign\": false,\n    \"_id\": \"83bG80eqFP/o2NV5ksY7Fq\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"4\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 34\n    },\n    \"_children\": [\n      {\n        \"__id__\": 183\n      },\n      {\n        \"__id__\": 192\n      },\n      {\n        \"__id__\": 200\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 211\n      },\n      {\n        \"__id__\": 212\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -9,\n      \"y\": -190,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"6b+uVBBONG1bvZyoIxUhsN\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 182\n    },\n    \"_children\": [\n      {\n        \"__id__\": 184\n      },\n      {\n        \"__id__\": 187\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 190\n      },\n      {\n        \"__id__\": 191\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.51220703125,\n      \"y\": 57.2,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"d9Ei+nZz5GzI1vnOamv5Xn\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"line\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 183\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 185\n      },\n      {\n        \"__id__\": 186\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.69,\n      \"y\": -33.781,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"cbp4Le/t1CJJZzvW7Sw+Rs\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 184\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 2\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"d3DDTHYulCb4q5qomzZw/0\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 184\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"cboC3D4XRLRbr2jeuOToUl\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"4\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 183\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 188\n      },\n      {\n        \"__id__\": 189\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 23,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"8fCWu760dLSbDBLNR636pa\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 187\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 78,\n      \"height\": 91\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"feMokIxu1D8pYp+PYtqwAa\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 187\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"795fc47a-0869-4991-ab76-e550753b908f@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 1,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"597LfdGLRG9Zg6J/S11bw0\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 183\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 365.0244140625,\n      \"height\": 75.6\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"d8FizI37RGeLvBwBq+nvJS\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 183\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"HIT                               WIN\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 30,\n    \"_fontSize\": 30,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 60,\n    \"_overflow\": 0,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"54mOzP9GpPoLL71AFpP+Ee\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 182\n    },\n    \"_children\": [\n      {\n        \"__id__\": 193\n      },\n      {\n        \"__id__\": 196\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 199\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -8.599999999999994,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"64C8SP1kpNOqPrlt+LtN6C\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 192\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 194\n      },\n      {\n        \"__id__\": 195\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"50WiPBMBBCL6x0LrbWvAJb\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 193\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"ce9hWiPwlFmYj1ASNFjlSC\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 193\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"4\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"d4Vxpoc/FHG7fxyNdGRGH8\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 192\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 197\n      },\n      {\n        \"__id__\": 198\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"4c6h3FPf1EJpV/QRE2koMS\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 196\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"b2y6D6T8BKYKCZhC8LHtIN\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 196\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"175x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"b85/eeZMpAVJ4np8zVFg0W\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 192\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"24kaJaf/BEf5lCmM7XGurQ\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 182\n    },\n    \"_children\": [\n      {\n        \"__id__\": 201\n      },\n      {\n        \"__id__\": 204\n      },\n      {\n        \"__id__\": 207\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 210\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -64.6,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"c7xeO4UrRExYFFhSISjKC8\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"SpriteSplash\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 200\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 202\n      },\n      {\n        \"__id__\": 203\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"f6vkoc4ApB+a3nBkpF/Wle\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 201\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"764g3PSalHa6o20a6UAzk2\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 201\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 30\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"58WxFxKwlBt4dYf2yqRFGC\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 200\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 205\n      },\n      {\n        \"__id__\": 206\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"1fpmX0h45EspQXhQ/UjaEn\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 204\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"efVTmrIWJIwKWZcQFV+F/7\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 204\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"3\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"73rmJRFTRJfLEvBjE0yDj0\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 200\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 208\n      },\n      {\n        \"__id__\": 209\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"2asw2BLUZGw60WlRwg7xaQ\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 207\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"ebgAlzgkVOc7VyXRKxp2AD\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 207\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"10x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"7cHNGtzHtI/qeUrMZEf8/G\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 200\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"92CGX1SghC/ptLYIesuvfp\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 182\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 190\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"209qyrRKhOE5TFVYr9RL+z\"\n  },\n  {\n    \"__type__\": \"cc.Layout\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 182\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_resizeMode\": 0,\n    \"_layoutType\": 3,\n    \"_cellSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 40,\n      \"height\": 40\n    },\n    \"_startAxis\": 0,\n    \"_paddingLeft\": 0,\n    \"_paddingRight\": 0,\n    \"_paddingTop\": 0,\n    \"_paddingBottom\": 0,\n    \"_spacingX\": 0,\n    \"_spacingY\": 0,\n    \"_verticalDirection\": 1,\n    \"_horizontalDirection\": 0,\n    \"_constraint\": 0,\n    \"_constraintNum\": 2,\n    \"_affectedByScale\": false,\n    \"_isAlign\": false,\n    \"_id\": \"49PlLEVOxIkouAEQJjqC5u\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"6\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 34\n    },\n    \"_children\": [\n      {\n        \"__id__\": 214\n      },\n      {\n        \"__id__\": 223\n      },\n      {\n        \"__id__\": 231\n      },\n      {\n        \"__id__\": 242\n      },\n      {\n        \"__id__\": 250\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 261\n      },\n      {\n        \"__id__\": 262\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 400,\n      \"y\": 150,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"5cPhEanHREqJgDUJGveAHY\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 213\n    },\n    \"_children\": [\n      {\n        \"__id__\": 215\n      },\n      {\n        \"__id__\": 218\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 221\n      },\n      {\n        \"__id__\": 222\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.51220703125,\n      \"y\": 112.2,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"87DJ8hysxNG7kVJ8kHxKAT\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"line\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 214\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 216\n      },\n      {\n        \"__id__\": 217\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.69,\n      \"y\": -33.781,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"e9fhUC75xOGp4yvI+PnQoD\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 215\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 2\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"2cDCWPFWpJaJ5RrSVTyVqv\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 215\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"bdrjKWs8dPwYPGRK+2pNqr\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"6\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 214\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 219\n      },\n      {\n        \"__id__\": 220\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 23,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"22Uz+DVvRPsKl+179reh9V\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 218\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 80,\n      \"height\": 97\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"f94Z/se31Im7N92HzY6yFW\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 218\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"a3eebb65-8e2d-45e0-adbb-1a4a319fb3ac@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 1,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"4c9AtD3IpI6YYkQYFXnwYm\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 214\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 365.0244140625,\n      \"height\": 75.6\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"74QArxzdlA6KoaOUOOevic\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 214\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"HIT                               WIN\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 30,\n    \"_fontSize\": 30,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 60,\n    \"_overflow\": 0,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"0d8yaksehCD63CEQC4z7u8\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 213\n    },\n    \"_children\": [\n      {\n        \"__id__\": 224\n      },\n      {\n        \"__id__\": 227\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 230\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 46.400000000000006,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"edqFG3HHlJnKzS0/otMQnO\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 223\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 225\n      },\n      {\n        \"__id__\": 226\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"48OrH5JhdDS4lIpvLDTFyH\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 224\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"2a0n2xSrpLHYJs+1EtpMkH\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 224\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"6\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"fdeoz9A3BJ4r+MVJqd2rgw\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 223\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 228\n      },\n      {\n        \"__id__\": 229\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"c5l8f2oL1NdI++y45q0Ov1\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 227\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"58GeqodH1CbpyatK0OvEOY\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 227\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"2 250x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"34P2TDoH1Kl4WsGbV3LXDT\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 223\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"46NhHWMAJFKavQYYkphbwK\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 213\n    },\n    \"_children\": [\n      {\n        \"__id__\": 232\n      },\n      {\n        \"__id__\": 235\n      },\n      {\n        \"__id__\": 238\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 241\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -9.599999999999994,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"5cJ5gxayRF0YD6hQE9aFjp\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"SpriteSplash\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 231\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 233\n      },\n      {\n        \"__id__\": 234\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"08v1GtIhNC97RArfnqfx+5\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 232\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"daF05fsy9NSbpPc7RtpIME\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 232\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 30\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"e8fpgu1GNE4Ybi1zPn5Lvg\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 231\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 236\n      },\n      {\n        \"__id__\": 237\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"55bV0Id2ZGUpKZha2ubXKM\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 235\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"8cZPLQhxlHzJldB7nQwkQJ\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 235\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"5\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"93ELYwEStCeYBmNlZtYHoD\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 231\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 239\n      },\n      {\n        \"__id__\": 240\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"56ONnJ30ZDtJPQP7okyGc6\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 238\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"d9zUY4jz9DOYtpLVOTD2M1\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 238\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"150x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"f9hPzosk1BWL+4CUXyP+YC\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 231\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"696qPtynVMwoejfnFzOoon\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node-002\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 213\n    },\n    \"_children\": [\n      {\n        \"__id__\": 243\n      },\n      {\n        \"__id__\": 246\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 249\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -65.6,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"10EYLm8C9NZLxuM/OmqHN+\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 242\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 244\n      },\n      {\n        \"__id__\": 245\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"f7VoCB2fhGRqp9vhqfGrBF\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 243\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"6cPRiFp15ACbdJEweEuADG\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 243\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"4\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"a3JAZab1FBSL0HSlgumawX\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 242\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 247\n      },\n      {\n        \"__id__\": 248\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"d1LIwiKTJMjotXMBbb/r96\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 246\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"f56lON6J9NSoCLDE49BQ4z\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 246\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"3x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"5fmJOijX9AHJKwnYfIkfd7\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 242\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"0cLkltqXBKW6ijgzd8LCx/\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node-003\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 213\n    },\n    \"_children\": [\n      {\n        \"__id__\": 251\n      },\n      {\n        \"__id__\": 254\n      },\n      {\n        \"__id__\": 257\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 260\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -121.60000000000002,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"56fztoN1hNcb31r53NHhtD\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"SpriteSplash\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 250\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 252\n      },\n      {\n        \"__id__\": 253\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"9eVFZLBRZGA4gyq7X1BNvg\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 251\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"ffOgdrpWRBcqyPMSYfX4yv\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 251\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 30\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"476PFtD/lC7YzQVQHfBjyE\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 250\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 255\n      },\n      {\n        \"__id__\": 256\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"c46H70eB1DZY3DQuEwMpMD\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 254\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"98VU+/kolBnI1m9Ica7zKH\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 254\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"3\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"d5erVs9+JChLgJpqliDpPi\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 250\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 258\n      },\n      {\n        \"__id__\": 259\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"2d7hdQzVpL86CHM0AksX4z\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 257\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"8bkWvKoNRF4aB2t6sx3w7z\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 257\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"1x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"c4NmBxzd1PIa3NlDnPF2yb\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 250\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"778cY0bBpI8q+QJXdxxYKI\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 213\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 300\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"52MsLBVrxDAphigUWrNI0c\"\n  },\n  {\n    \"__type__\": \"cc.Layout\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 213\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_resizeMode\": 0,\n    \"_layoutType\": 3,\n    \"_cellSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 40,\n      \"height\": 40\n    },\n    \"_startAxis\": 0,\n    \"_paddingLeft\": 0,\n    \"_paddingRight\": 0,\n    \"_paddingTop\": 0,\n    \"_paddingBottom\": 0,\n    \"_spacingX\": 0,\n    \"_spacingY\": 0,\n    \"_verticalDirection\": 1,\n    \"_horizontalDirection\": 0,\n    \"_constraint\": 0,\n    \"_constraintNum\": 2,\n    \"_affectedByScale\": false,\n    \"_isAlign\": false,\n    \"_id\": \"a52bbPH5VFRbp6TnZ6BeI0\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"5\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 34\n    },\n    \"_children\": [\n      {\n        \"__id__\": 264\n      },\n      {\n        \"__id__\": 273\n      },\n      {\n        \"__id__\": 281\n      },\n      {\n        \"__id__\": 292\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 300\n      },\n      {\n        \"__id__\": 301\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 400,\n      \"y\": -167.5,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"b0Y6g+S1dHTqPxLDf13wtD\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 263\n    },\n    \"_children\": [\n      {\n        \"__id__\": 265\n      },\n      {\n        \"__id__\": 268\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 271\n      },\n      {\n        \"__id__\": 272\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.51220703125,\n      \"y\": 84.7,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"c1ik+gCGJPYK6ZOld1Re4W\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"line\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 264\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 266\n      },\n      {\n        \"__id__\": 267\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.69,\n      \"y\": -33.781,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"7aJfYuUwdLuL2rJN++OyME\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 265\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 2\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"72W5UDZA5Aa5p09WQt5i4a\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 265\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"517+hcu41DO7vC8Js+/v6s\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"5\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 264\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 269\n      },\n      {\n        \"__id__\": 270\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 23,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"7aawVNDNNJOaBxeCX6fDnC\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 268\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 71,\n      \"height\": 92\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"ecDf5pN1dFRKPTjbjpUKa/\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 268\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"b8ecca1c-faad-4405-b483-a2a0c63300c2@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 1,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"7cSNUBEMxK3bRUz7R3i8Lm\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 264\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 365.0244140625,\n      \"height\": 75.6\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"02c5q2E3xMGbc9mcQYYrJ+\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 264\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"HIT                               WIN\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 30,\n    \"_fontSize\": 30,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 60,\n    \"_overflow\": 0,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"7bHY7YPXZCCYqC3HaQ+sxm\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 263\n    },\n    \"_children\": [\n      {\n        \"__id__\": 274\n      },\n      {\n        \"__id__\": 277\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 280\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 18.900000000000006,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"dfHw1YbFFIeZjy2cZN8VNA\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 273\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 275\n      },\n      {\n        \"__id__\": 276\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"8338ZLMyFHs7koQAGnl+nQ\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 274\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"5dYMy9HyhCVbb5M6lDsimZ\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 274\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"5\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"a47kwHs0dDraN0+QDL0Z5f\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 273\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 278\n      },\n      {\n        \"__id__\": 279\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"00sJpK+TZHIYkeG2+BMDN+\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 277\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"87lm3Hd8JAKYL4E6WUjjdM\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 277\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"275x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"73CFpFes5KH6+bU6q7SpsV\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 273\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"75XRP8pO5JeqAjAapGBAY6\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 263\n    },\n    \"_children\": [\n      {\n        \"__id__\": 282\n      },\n      {\n        \"__id__\": 285\n      },\n      {\n        \"__id__\": 288\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 291\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -37.099999999999994,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"6ePaOPtIFCo6QjoOYlyvMF\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"SpriteSplash\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 281\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 283\n      },\n      {\n        \"__id__\": 284\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"17W7JxMzVDBZm9ISnXx9ON\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 282\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"bcM2SjdatDzIN/Z2HQbj2V\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 282\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 30\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"21/bQARxVI26ocWm8ETBug\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 281\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 286\n      },\n      {\n        \"__id__\": 287\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"8akNlcRG9CLKMdUdM/TX32\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 285\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"a86IfWRKBPMLlK32HxgKAp\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 285\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"4\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"0azVscWiZFMoQVUtsxGIzi\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 281\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 289\n      },\n      {\n        \"__id__\": 290\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"a1MJMziT1Fhr7ruIwrRQN/\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 288\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"aeLXTT5nlCeI9uHMaaNO3g\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 288\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"10x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"c3ExQWvqBHyKcN9iDUQUt4\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 281\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"2cpYXFUapCYLX+BQ4/dskw\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Node-002\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 263\n    },\n    \"_children\": [\n      {\n        \"__id__\": 293\n      },\n      {\n        \"__id__\": 296\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 299\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -93.1,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"eb2gZ9SrhKcIcfrQ3A6hC/\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 292\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 294\n      },\n      {\n        \"__id__\": 295\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -162,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"5fcEDm5qlIlqNAdMBfGO3J\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 293\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"d1szubLN9M2pRdma677c0P\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 293\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"3\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"d3flJCDMBIMYcxIs+TrE4z\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label-001\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 292\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 297\n      },\n      {\n        \"__id__\": 298\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 76.202,\n      \"y\": 0.962,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"f4Qd0w+xxNbbdp2Tkf54xD\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 296\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"57hEyuUHdJ/ZbxPnNgfMdd\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 296\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 218,\n      \"b\": 68,\n      \"a\": 255\n    },\n    \"_string\": \"8x\",\n    \"_horizontalAlign\": 2,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 93,\n      \"g\": 65,\n      \"b\": 191,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": -3\n    },\n    \"_shadowBlur\": 6,\n    \"_id\": \"104sYTJAFOGpTSuAxKLRKk\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 292\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 56\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"07p955CPxAhKL56uFcMsao\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 263\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 364,\n      \"height\": 245\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"7fsZ4Quj1DZrMO1NrMbfWP\"\n  },\n  {\n    \"__type__\": \"cc.Layout\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 263\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_resizeMode\": 0,\n    \"_layoutType\": 3,\n    \"_cellSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 40,\n      \"height\": 40\n    },\n    \"_startAxis\": 0,\n    \"_paddingLeft\": 0,\n    \"_paddingRight\": 0,\n    \"_paddingTop\": 0,\n    \"_paddingBottom\": 0,\n    \"_spacingX\": 0,\n    \"_spacingY\": 0,\n    \"_verticalDirection\": 1,\n    \"_horizontalDirection\": 0,\n    \"_constraint\": 0,\n    \"_constraintNum\": 2,\n    \"_affectedByScale\": false,\n    \"_isAlign\": false,\n    \"_id\": \"ebfiiNKc5JUJ2H7utLhUOI\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 34\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 1200,\n      \"height\": 600\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"d27YNThdBIwodwx+E6voXs\"\n  },\n  {\n    \"__type__\": \"cc.Layout\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 34\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_resizeMode\": 0,\n    \"_layoutType\": 3,\n    \"_cellSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 40,\n      \"height\": 40\n    },\n    \"_startAxis\": 1,\n    \"_paddingLeft\": 0,\n    \"_paddingRight\": 0,\n    \"_paddingTop\": 0,\n    \"_paddingBottom\": 0,\n    \"_spacingX\": 45,\n    \"_spacingY\": 45,\n    \"_verticalDirection\": 1,\n    \"_horizontalDirection\": 0,\n    \"_constraint\": 1,\n    \"_constraintNum\": 2,\n    \"_affectedByScale\": false,\n    \"_isAlign\": false,\n    \"_id\": \"59NlxIBS9Am4qOOY4j/6tT\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"lucky\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 21\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 305\n      },\n      {\n        \"__id__\": 306\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -154.073,\n      \"y\": 386.877,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1.2,\n      \"y\": 1.2,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"2bAehFnKhHv5jkl0vmUPgW\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 304\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 281,\n      \"height\": 153\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"d6Jwtswu1DxLzUSlY0bprS\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 304\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"8a3eb309-f4fa-4dbb-a38d-e08034caa4c3@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 1,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"5eFGQGwvFDmrOtS1VddzSO\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 21\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 1920,\n      \"height\": 1080\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"47sGXMW2xD963ZbGYxYke8\"\n  },\n  {\n    \"__type__\": \"cc.UIOpacity\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 21\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_opacity\": 255,\n    \"_id\": \"84vbn6nGZDFLk/+ByPy/Yp\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_prefab\": {\n      \"__id__\": 310\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 309\n    },\n    \"asset\": {\n      \"__uuid__\": \"952a345c-ac9c-4fb2-8fc1-0f293fc2f1f1\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"a7Ql7Tr5RBJbeXSFr8sVyw\",\n    \"instance\": {\n      \"__id__\": 311\n    },\n    \"targetOverrides\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"124w67EWhHH5YU+rWdVaBt\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 312\n      },\n      {\n        \"__id__\": 314\n      },\n      {\n        \"__id__\": 316\n      },\n      {\n        \"__id__\": 318\n      },\n      {\n        \"__id__\": 320\n      },\n      {\n        \"__id__\": 322\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 313\n    },\n    \"propertyPath\": [\n      \"_active\"\n    ],\n    \"value\": false\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"a7Ql7Tr5RBJbeXSFr8sVyw\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 315\n    },\n    \"propertyPath\": [\n      \"_contentSize\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 497.734375,\n      \"height\": 100.8\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"860xHk/MZJIoGUErLeelqw\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 317\n    },\n    \"propertyPath\": [\n      \"_contentSize\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 21.1337890625,\n      \"height\": 56.7\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"d2mcO0w4hAQpptuvBRD8W7\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 319\n    },\n    \"propertyPath\": [\n      \"_contentSize\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 21.1337890625,\n      \"height\": 56.7\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"aa+PvO98hIdIF2PuJ6vK3d\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 321\n    },\n    \"propertyPath\": [\n      \"_contentSize\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 21.1337890625,\n      \"height\": 56.7\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"b95U1vWjxCgaoFd42/RN1j\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 323\n    },\n    \"propertyPath\": [\n      \"_contentSize\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 21.1337890625,\n      \"height\": 56.7\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"50FJ1wl5JDbrZB0oBX5wj8\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_prefab\": {\n      \"__id__\": 325\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 324\n    },\n    \"asset\": {\n      \"__uuid__\": \"1dd1efdc-128e-4dd9-a6ff-a067302faaa8\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"b6yJ62YT5OMZjj5hL6edL8\",\n    \"instance\": {\n      \"__id__\": 326\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"f9A+aAgJNFpJkgzquuHovX\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 327\n      },\n      {\n        \"__id__\": 329\n      },\n      {\n        \"__id__\": 331\n      },\n      {\n        \"__id__\": 333\n      },\n      {\n        \"__id__\": 335\n      },\n      {\n        \"__id__\": 337\n      },\n      {\n        \"__id__\": 339\n      },\n      {\n        \"__id__\": 341\n      },\n      {\n        \"__id__\": 343\n      },\n      {\n        \"__id__\": 345\n      },\n      {\n        \"__id__\": 347\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 328\n    },\n    \"propertyPath\": [\n      \"_name\"\n    ],\n    \"value\": \"colorPanel\"\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"b6yJ62YT5OMZjj5hL6edL8\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 330\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"b6yJ62YT5OMZjj5hL6edL8\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 332\n    },\n    \"propertyPath\": [\n      \"_lrot\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"b6yJ62YT5OMZjj5hL6edL8\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 334\n    },\n    \"propertyPath\": [\n      \"_euler\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"b6yJ62YT5OMZjj5hL6edL8\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 336\n    },\n    \"propertyPath\": [\n      \"_type\"\n    ],\n    \"value\": 1\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"db8vgPfBZNAqveLnWQloAD\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 338\n    },\n    \"propertyPath\": [\n      \"_active\"\n    ],\n    \"value\": false\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"b6yJ62YT5OMZjj5hL6edL8\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 340\n    },\n    \"propertyPath\": [\n      \"_contentSize\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 62.203125,\n      \"height\": 63\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"9brfHQBJ1Ki6+VOpU/LXii\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 342\n    },\n    \"propertyPath\": [\n      \"_contentSize\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 19.46533203125,\n      \"height\": 50.4\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"3f3gcQcdtBkq1wBk9w6BtD\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 344\n    },\n    \"propertyPath\": [\n      \"_contentSize\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 19.46533203125,\n      \"height\": 50.4\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"baEdSP8FNMeoe3H5M88inA\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 346\n    },\n    \"propertyPath\": [\n      \"_contentSize\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 19.46533203125,\n      \"height\": 50.4\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"1biMN8XPtMUYw+np/8iV/j\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 348\n    },\n    \"propertyPath\": [\n      \"_contentSize\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 19.46533203125,\n      \"height\": 50.4\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"dcmWtpwJpCuK8icvw0Bx5n\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_prefab\": {\n      \"__id__\": 350\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 349\n    },\n    \"asset\": {\n      \"__uuid__\": \"b27443b0-a55c-4e49-868f-1a8b588076ff\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"00ShGuOWhPbLQJs5xdcJpy\",\n    \"instance\": {\n      \"__id__\": 351\n    },\n    \"targetOverrides\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"cd6vIDVcJHqqQ/XW0DK99k\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 352\n      },\n      {\n        \"__id__\": 354\n      },\n      {\n        \"__id__\": 356\n      },\n      {\n        \"__id__\": 358\n      },\n      {\n        \"__id__\": 360\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 353\n    },\n    \"propertyPath\": [\n      \"_name\"\n    ],\n    \"value\": \"ballsPanel\"\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"00ShGuOWhPbLQJs5xdcJpy\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 355\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"00ShGuOWhPbLQJs5xdcJpy\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 357\n    },\n    \"propertyPath\": [\n      \"_lrot\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"00ShGuOWhPbLQJs5xdcJpy\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 359\n    },\n    \"propertyPath\": [\n      \"_euler\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"00ShGuOWhPbLQJs5xdcJpy\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 361\n    },\n    \"propertyPath\": [\n      \"_active\"\n    ],\n    \"value\": false\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"00ShGuOWhPbLQJs5xdcJpy\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_prefab\": {\n      \"__id__\": 363\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 362\n    },\n    \"asset\": {\n      \"__uuid__\": \"a7a8c9a9-f960-42c2-9464-7bad59c3390d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"2cqhGkLZ1JKYZgisO3H4dF\",\n    \"instance\": {\n      \"__id__\": 364\n    },\n    \"targetOverrides\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"c8tVQTlkRPJorzTdThZc2J\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 365\n      },\n      {\n        \"__id__\": 367\n      },\n      {\n        \"__id__\": 369\n      },\n      {\n        \"__id__\": 371\n      },\n      {\n        \"__id__\": 373\n      },\n      {\n        \"__id__\": 375\n      },\n      {\n        \"__id__\": 377\n      },\n      {\n        \"__id__\": 379\n      },\n      {\n        \"__id__\": 381\n      },\n      {\n        \"__id__\": 383\n      },\n      {\n        \"__id__\": 385\n      },\n      {\n        \"__id__\": 387\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 366\n    },\n    \"propertyPath\": [\n      \"_name\"\n    ],\n    \"value\": \"spawn\"\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"2cqhGkLZ1JKYZgisO3H4dF\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 368\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -641.854,\n      \"y\": -358.27200000000005,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"2cqhGkLZ1JKYZgisO3H4dF\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 370\n    },\n    \"propertyPath\": [\n      \"_lrot\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"2cqhGkLZ1JKYZgisO3H4dF\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 372\n    },\n    \"propertyPath\": [\n      \"_euler\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"2cqhGkLZ1JKYZgisO3H4dF\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 374\n    },\n    \"propertyPath\": [\n      \"_active\"\n    ],\n    \"value\": true\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"2cqhGkLZ1JKYZgisO3H4dF\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 376\n    },\n    \"propertyPath\": [\n      \"_active\"\n    ],\n    \"value\": true\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"f2j23ZGUtGXIErEk4SqiBk\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 378\n    },\n    \"propertyPath\": [\n      \"_string\"\n    ],\n    \"value\": \"\"\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"ador5dmDpHMLn8NXKGBM4b\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 380\n    },\n    \"propertyPath\": [\n      \"_string\"\n    ],\n    \"value\": \"\"\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"0eZHFdjNxAeZW8Ml/0uGmF\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 382\n    },\n    \"propertyPath\": [\n      \"_actualFontSize\"\n    ],\n    \"value\": 120\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"0eZHFdjNxAeZW8Ml/0uGmF\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 384\n    },\n    \"propertyPath\": [\n      \"_active\"\n    ],\n    \"value\": false\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"e351GMGzVKbrtAuCKlgRfO\",\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 386\n    },\n    \"propertyPath\": [\n      \"_active\"\n    ],\n    \"value\": true\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"6fbKih2NtC5qp68wjvhcZS\",\n      \"a70+7iLXlHAaIh265DvNEL\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 388\n    },\n    \"propertyPath\": [\n      \"_active\"\n    ],\n    \"value\": false\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"6fbKih2NtC5qp68wjvhcZS\",\n      \"dfBNBPv9BA1a0HTZ3RktuS\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 1280,\n      \"height\": 720\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"d6rUX5yfhMlKoWX2bSbawx\"\n  },\n  {\n    \"__type__\": \"cc.Canvas\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_cameraComponent\": {\n      \"__id__\": 4\n    },\n    \"_alignCanvasWithScreen\": true,\n    \"_id\": \"12O/ljcVlEqLmVm3U2gEOQ\"\n  },\n  {\n    \"__type__\": \"cc.Widget\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_alignFlags\": 45,\n    \"_target\": null,\n    \"_left\": 0,\n    \"_right\": 0,\n    \"_top\": 5.684341886080802e-14,\n    \"_bottom\": 5.684341886080802e-14,\n    \"_horizontalCenter\": 0,\n    \"_verticalCenter\": 0,\n    \"_isAbsLeft\": true,\n    \"_isAbsRight\": true,\n    \"_isAbsTop\": true,\n    \"_isAbsBottom\": true,\n    \"_isAbsHorizontalCenter\": true,\n    \"_isAbsVerticalCenter\": true,\n    \"_originalWidth\": 0,\n    \"_originalHeight\": 0,\n    \"_alignMode\": 2,\n    \"_lockFlags\": 0,\n    \"_id\": \"c5V1EV8IpMtrIvY1OE9t2u\"\n  },\n  {\n    \"__type__\": \"34681qRBpxNmo8YeSez6UqS\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"mainPanel\": {\n      \"__id__\": 8\n    },\n    \"instructionPanel\": {\n      \"__id__\": 21\n    },\n    \"suggestPanel\": {\n      \"__id__\": 309\n    },\n    \"colorPanel\": {\n      \"__id__\": 324\n    },\n    \"numberSpawner\": null,\n    \"ballsPanel\": {\n      \"__id__\": 349\n    },\n    \"_id\": \"3ffE4N1uRIXbbZdOTlpoo3\"\n  },\n  {\n    \"__type__\": \"1a98cFC0qNFAqyIMKQ8kDsv\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"adapterMode\": 2,\n    \"_id\": \"b9m1aHWUJH+6qpyIwc2Sbi\"\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": null,\n    \"asset\": null,\n    \"fileId\": \"bb178f2a-b8ec-40c5-b625-fa77e0041139\",\n    \"instance\": null,\n    \"targetOverrides\": [\n      {\n        \"__id__\": 395\n      }\n    ],\n    \"nestedPrefabInstanceRoots\": [\n      {\n        \"__id__\": 8\n      },\n      {\n        \"__id__\": 309\n      },\n      {\n        \"__id__\": 324\n      },\n      {\n        \"__id__\": 349\n      },\n      {\n        \"__id__\": 362\n      }\n    ]\n  },\n  {\n    \"__type__\": \"cc.TargetOverrideInfo\",\n    \"source\": {\n      \"__id__\": 392\n    },\n    \"sourceInfo\": null,\n    \"propertyPath\": [\n      \"numberSpawner\"\n    ],\n    \"target\": {\n      \"__id__\": 362\n    },\n    \"targetInfo\": {\n      \"__id__\": 396\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"ddbAuJBfFEaYHbHpZWihBg\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.SceneGlobals\",\n    \"ambient\": {\n      \"__id__\": 398\n    },\n    \"shadows\": {\n      \"__id__\": 399\n    },\n    \"_skybox\": {\n      \"__id__\": 400\n    },\n    \"fog\": {\n      \"__id__\": 401\n    },\n    \"octree\": {\n      \"__id__\": 402\n    },\n    \"skin\": {\n      \"__id__\": 403\n    },\n    \"lightProbeInfo\": {\n      \"__id__\": 404\n    },\n    \"postSettings\": {\n      \"__id__\": 405\n    },\n    \"bakedWithStationaryMainLight\": false,\n    \"bakedWithHighpLightmap\": false\n  },\n  {\n    \"__type__\": \"cc.AmbientInfo\",\n    \"_skyColorHDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0.520833125\n    },\n    \"_skyColor\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0.520833125\n    },\n    \"_skyIllumHDR\": 20000,\n    \"_skyIllum\": 20000,\n    \"_groundAlbedoHDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0\n    },\n    \"_groundAlbedo\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0\n    },\n    \"_skyColorLDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.2,\n      \"y\": 0.5,\n      \"z\": 0.8,\n      \"w\": 1\n    },\n    \"_skyIllumLDR\": 20000,\n    \"_groundAlbedoLDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.2,\n      \"y\": 0.2,\n      \"z\": 0.2,\n      \"w\": 1\n    }\n  },\n  {\n    \"__type__\": \"cc.ShadowsInfo\",\n    \"_enabled\": false,\n    \"_type\": 0,\n    \"_normal\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 1,\n      \"z\": 0\n    },\n    \"_distance\": 0,\n    \"_planeBias\": 1,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 76,\n      \"g\": 76,\n      \"b\": 76,\n      \"a\": 255\n    },\n    \"_maxReceived\": 4,\n    \"_size\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 512,\n      \"y\": 512\n    }\n  },\n  {\n    \"__type__\": \"cc.SkyboxInfo\",\n    \"_envLightingType\": 0,\n    \"_envmapHDR\": null,\n    \"_envmap\": null,\n    \"_envmapLDR\": null,\n    \"_diffuseMapHDR\": null,\n    \"_diffuseMapLDR\": null,\n    \"_enabled\": false,\n    \"_useHDR\": true,\n    \"_editableMaterial\": null,\n    \"_reflectionHDR\": null,\n    \"_reflectionLDR\": null,\n    \"_rotationAngle\": 0\n  },\n  {\n    \"__type__\": \"cc.FogInfo\",\n    \"_type\": 0,\n    \"_fogColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 200,\n      \"g\": 200,\n      \"b\": 200,\n      \"a\": 255\n    },\n    \"_enabled\": false,\n    \"_fogDensity\": 0.3,\n    \"_fogStart\": 0.5,\n    \"_fogEnd\": 300,\n    \"_fogAtten\": 5,\n    \"_fogTop\": 1.5,\n    \"_fogRange\": 1.2,\n    \"_accurate\": false\n  },\n  {\n    \"__type__\": \"cc.OctreeInfo\",\n    \"_enabled\": false,\n    \"_minPos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -1024,\n      \"y\": -1024,\n      \"z\": -1024\n    },\n    \"_maxPos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1024,\n      \"y\": 1024,\n      \"z\": 1024\n    },\n    \"_depth\": 8\n  },\n  {\n    \"__type__\": \"cc.SkinInfo\",\n    \"_enabled\": false,\n    \"_blurRadius\": 0.01,\n    \"_sssIntensity\": 3\n  },\n  {\n    \"__type__\": \"cc.LightProbeInfo\",\n    \"_giScale\": 1,\n    \"_giSamples\": 1024,\n    \"_bounces\": 2,\n    \"_reduceRinging\": 0,\n    \"_showProbe\": true,\n    \"_showWireframe\": true,\n    \"_showConvex\": false,\n    \"_data\": null,\n    \"_lightProbeSphereVolume\": 1\n  },\n  {\n    \"__type__\": \"cc.PostSettingsInfo\",\n    \"_toneMappingType\": 0\n  }\n]"