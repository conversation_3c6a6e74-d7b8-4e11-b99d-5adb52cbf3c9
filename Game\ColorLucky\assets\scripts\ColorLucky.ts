import { _decorator, Component, easing, EPhysics2DDrawFlags, Label, Node, PhysicsSystem2D, Prefab, Sprite, SpriteFrame, tween, UITransform } from 'cc';
import { TweenUtils } from '../framework/utils/TweenUtils';
import { BallsPanel } from './panel/BallsPanel';
import FrameWebSocket from '../framework/network/websocket';
import { MainPanel } from './panel/MainPanel';
import { SuggestPanel } from './panel/SuggestPanel';
import { NumberSpawner } from './NumberSpawner';
import { eventManager } from '../framework/event/EventManager';
const { ccclass, property } = _decorator;

@ccclass('ColorLucky')
export class ColorLucky extends Component {

    @property(Node)
    private mainPanel: Node = null;

    @property(Node)
    private instructionPanel: Node = null;

    @property(Node)
    private suggestPanel: Node = null;

    @property(Node)
    private colorPanel: Node = null;

    @property(NumberSpawner)
    private numberSpawner: NumberSpawner = null;

    @property(Node)
    private ballsPanel: Node = null;


    private wsUrl: string = "ws://106.14.93.173:9090/?match=3000";

    private socket: FrameWebSocket = null;
    private taskId: number = 0;
    private dataSource: any = null;

    private remainingSeconds: number = 0;


    private locked: boolean = false;

    protected onLoad(): void {
        // PhysicsSystem2D.instance.enable = true;
        // PhysicsSystem2D.instance.debugDrawFlags = EPhysics2DDrawFlags.Aabb |
        //     EPhysics2DDrawFlags.Pair |
        //     EPhysics2DDrawFlags.CenterOfMass |
        //     EPhysics2DDrawFlags.Joint |
        //     EPhysics2DDrawFlags.Shape;

        eventManager.on("BallsPanel_Finish", this.onBallsFinish.bind(this));
    }

    onBallsFinish() {
        this.locked = false;
        this.showMainPanel();
    }


    start() {

        this.socket = new FrameWebSocket({
            url: this.wsUrl,
            reconnectInterval: 1000 * 3,
            maxReconnectAttempts: 0,
            heartbeatInterval: 1000 * 5,
            heartbeat: () => {
                return {
                    command: 'ping',
                    clientTimestamp: Date.now()
                }
            }
        })

        this.socket.on('message', this.onWsMessage.bind(this));



        // let countdown = 3;

        // this.schedule(() => {

        //     this.numberSpawnner.countdown(countdown);

        //     //显示说明面板
        //     if (countdown == 45) {
        //         this.showInstructionPanel();
        //     }
        //     //显示hot cold 推荐面板
        //     if (countdown == 30) {
        //         this.showSuggestPanel();
        //     }
        //     //显示hot cold 推荐面板
        //     if (countdown == 15) {
        //         this.showColorPanel();
        //     }

        //     if (countdown == 0) {
        //         this.showResultPanel();
        //         this.startNumberAnimation();
        //     }

        //     countdown--;
        // }, 1, countdown);
    }

    onWsMessage(data: any) {
        switch (data.command) {
            case "ColorLuckyRecentResult":
                this.onRecentResult(data);
                break;
            case "ColorLuckyRoundResult":
                this.onRoundResult(data);
                break;
        }
    }


    /**
     * 最新结果数据
     * @param data 
     */
    onRecentResult(data: any) {

        this.dataSource = data;

        // 清除定时器
        clearInterval(this.taskId);
        this.unscheduleAllCallbacks();
        this.remainingSeconds = Math.round((data.nextTime - data.currentTime) / 1000);
        // this.remainingSeconds = 3;

        // this.numberSpawner.countdown(this.remainingSeconds);
        this.numberSpawner.setDataSource(this.dataSource);

        this.taskId = setInterval(this.updateCountDown.bind(this), 1 * 1000);

        // 显示主面板
        this.showMainPanel();
    }

    updateCountDown() {

        this.remainingSeconds--;

        if (this.locked) {
            return;
        }

        if (this.remainingSeconds <= 0) {
            clearInterval(this.taskId);
        }

        //显示玩法面板
        if (this.remainingSeconds == 45) {
            this.showInstructionPanel();
        }
        //显示hot cold 推荐面板
        if (this.remainingSeconds == 30) {
            this.showSuggestPanel();
        }

        //显示颜色面板
        if (this.remainingSeconds == 15) {
            this.showColorPanel();
        }
        console.log('remainingSeconds', this.remainingSeconds);

        // 显示小球结算面板
        // if (this.remainingSeconds == 0) {
        //     this.showBallsPanel();
        // }

        //倒计时
        this.numberSpawner.setDataSource(this.dataSource);
        this.numberSpawner.countdown(this.remainingSeconds);

    }

    onRoundResult(data?: any) {
        this.dataSource = data;
        this.numberSpawner.setDataSource(this.dataSource);

        clearInterval(this.taskId);
        this.unscheduleAllCallbacks();
        this.remainingSeconds = Math.round((data.nextTime - data.currentTime) / 1000);
        // this.remainingSeconds = 3;
        // this.numberSpawner.countdown(this.remainingSeconds);

        this.taskId = setInterval(this.updateCountDown.bind(this), 1 * 1000);
        //显示小球结果
        this.showBallsPanel();

    }

    showMainPanel() {
        this.mainPanel.getComponent(MainPanel).setDataSource(this.dataSource);
        TweenUtils.fadeOut(this.suggestPanel);
        TweenUtils.fadeOut(this.colorPanel);
        TweenUtils.fadeOut(this.ballsPanel);
        TweenUtils.fadeOut(this.instructionPanel);
        TweenUtils.fadeIn(this.mainPanel);
    }

    showInstructionPanel() {
        TweenUtils.fadeOut(this.mainPanel);
        TweenUtils.fadeOut(this.suggestPanel);
        TweenUtils.fadeOut(this.colorPanel);
        TweenUtils.fadeOut(this.ballsPanel);
        TweenUtils.fadeIn(this.instructionPanel);
    }

    showSuggestPanel() {
        this.suggestPanel.getComponent(SuggestPanel).setDataSource(this.dataSource);
        TweenUtils.fadeOut(this.mainPanel);
        TweenUtils.fadeOut(this.colorPanel);
        TweenUtils.fadeOut(this.ballsPanel);
        TweenUtils.fadeOut(this.instructionPanel);
        TweenUtils.fadeIn(this.suggestPanel);
    }

    showColorPanel() {
        TweenUtils.fadeOut(this.mainPanel);
        TweenUtils.fadeOut(this.ballsPanel);
        TweenUtils.fadeOut(this.instructionPanel);
        TweenUtils.fadeOut(this.suggestPanel);
        TweenUtils.fadeIn(this.colorPanel);
    }

    showBallsPanel() {
        this.locked = true;

        this.ballsPanel.getComponent(BallsPanel).setDataSource(this.dataSource, this.numberSpawner);

        TweenUtils.fadeOut(this.mainPanel);
        TweenUtils.fadeOut(this.colorPanel);
        TweenUtils.fadeOut(this.instructionPanel);
        TweenUtils.fadeOut(this.suggestPanel);
        TweenUtils.fadeIn(this.ballsPanel, 0.1);
    }

    protected onEnable(): void {

    }

    protected onDisable(): void {
        clearInterval(this.taskId);
        this.unscheduleAllCallbacks();
    }

    update(deltaTime: number) {

    }
}


