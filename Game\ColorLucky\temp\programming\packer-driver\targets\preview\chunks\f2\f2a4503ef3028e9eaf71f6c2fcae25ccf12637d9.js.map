{"version": 3, "sources": ["file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/perfabs/Led-001.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "Led", "start", "update", "deltaTime", "light", "fadeIn", "node", "getChildByName", "dark", "fadeOut"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AACZC,MAAAA,U,iBAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;qBAGjBK,G,WADZF,OAAO,CAAC,KAAD,C,gBAAR,MACaE,GADb,SACyBJ,SADzB,CACmC;AAC/BK,QAAAA,KAAK,GAAG,CAEP;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AAAA;AAAA,wCAAWC,MAAX,CAAkB,KAAKC,IAAL,CAAUC,cAAV,CAAyB,OAAzB,CAAlB,EAAqD,GAArD,EADI,CAEJ;AACH;;AAEDC,QAAAA,IAAI,GAAG;AACH;AAAA;AAAA,wCAAWC,OAAX,CAAmB,KAAKH,IAAL,CAAUC,cAAV,CAAyB,OAAzB,CAAnB,EAAsD,GAAtD,EADG,CAEH;AACH;;AAjB8B,O", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nimport { TweenUtils } from '../../framework/utils/TweenUtils';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Led')\r\nexport class Led extends Component {\r\n    start() {\r\n\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        \r\n    }\r\n\r\n    light() {\r\n        TweenUtils.fadeIn(this.node.getChildByName('light'), 0.1);\r\n        // this.node.getChildByName('light').active = true;\r\n    }\r\n\r\n    dark() {\r\n        TweenUtils.fadeOut(this.node.getChildByName('light'), 0.1);\r\n        // this.node.getChildByName('light').active = false;\r\n    }\r\n}\r\n\r\n\r\n"]}