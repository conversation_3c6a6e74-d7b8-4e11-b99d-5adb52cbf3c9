
export enum LogLevel {
    Debug = 0,
    Info = 1,
    Error = 2,
    None = 3
}

class Logger {
    static debug(message: any, ...args: any[]) {
        console.log("[IMSDK LOG]", message, ...args);
    }
    static info(message: any, ...args: any[]) {
        console.log("[IMSDK LOG]", message, ...args);
    }
    static error(message: any, ...args: any[]) {
        console.error("[IMSDK LOG]", message, ...args);
    }

}

/**
 * 日志输出
 * @param level 
 * @param message 
 */
export default Logger;