{"version": 3, "sources": ["file:///E:/Workspace/magic-match/Game/ColorLucky/assets/scripts/NumberSpawner.ts"], "names": ["_decorator", "Component", "instantiate", "Label", "Node", "Prefab", "tween", "Vec3", "Animation", "UIOpacity", "Tween", "Led", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FormatUtils", "BallItem", "eventManager", "ccclass", "property", "NumberSpawner", "currentIndex", "lightCount", "ledNodes", "numbers", "dataSource", "setDataSource", "data", "command", "node", "<PERSON><PERSON><PERSON><PERSON><PERSON>y<PERSON><PERSON>", "getComponent", "string", "nextRound", "onLoad", "on", "onBallsFinish", "bind", "onBallsStart", "centerX", "centerY", "radius", "numLights", "circleNode", "getChildByName", "i", "angle", "Math", "PI", "x", "cos", "y", "sin", "ledNode", "led", "setPosition", "<PERSON><PERSON><PERSON><PERSON>", "animationNode", "active", "logo", "for<PERSON>ach", "stopAllByTarget", "dark", "currentRound", "playBounceAnimation", "play", "btnAni", "opacity", "scale", "to", "then", "union", "start", "<PERSON><PERSON><PERSON><PERSON>", "bar", "showBigNumberAnimation", "number", "callback", "push", "bigBall", "bigBallOpacity", "setNumber", "delay", "call", "length", "light", "countdown", "seconds", "startFlowAnimation", "tick", "scaleAndRestore", "setScale", "duration", "formatDuration", "countdown<PERSON><PERSON>l", "objs", "value", "map", "lightsToShow", "lightsToHide", "index", "easing", "onEnable", "onDisable", "unscheduleAllCallbacks", "update", "deltaTime"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAmBC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAoBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;;AACjHC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,W,iBAAAA,W;;AAEAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBjB,U;;+BAGjBkB,a,WADZF,OAAO,CAAC,eAAD,C,UAGHC,QAAQ,CAACZ,MAAD,C,UAGRY,QAAQ,CAACd,KAAD,C,UAGRc,QAAQ,CAACb,IAAD,C,UAGRa,QAAQ,CAACb,IAAD,C,UAGRa,QAAQ,CAACb,IAAD,C,2BAfb,MACac,aADb,SACmCjB,SADnC,CAC6C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAiBjCkB,YAjBiC,GAiBV,CAjBU;AAAA,eAkBjCC,UAlBiC,GAkBZ,CAlBY;AAAA,eAoBjCC,QApBiC,GAoBf,EApBe;AAAA,eAsBjCC,OAtBiC,GAsBb,EAtBa;AAAA,eAwBjCC,UAxBiC,GAwBf,IAxBe;AAAA;;AA0BzCC,QAAAA,aAAa,CAACC,IAAD,EAAY;AACrB,eAAKF,UAAL,GAAkBE,IAAlB;;AAEA,cAAIA,IAAI,CAACC,OAAL,IAAgB,wBAApB,EAA8C;AAC1C,iBAAKC,IAAL,CAAUC,cAAV,CAAyB,eAAzB,EAA0CC,YAA1C,CAAuD1B,KAAvD,EAA8D2B,MAA9D,GAAuE,MAAvE;AACA,iBAAKH,IAAL,CAAUC,cAAV,CAAyB,aAAzB,EAAwCC,YAAxC,CAAqD1B,KAArD,EAA4D2B,MAA5D,GAAsE,MAAK,KAAKP,UAAL,CAAgBQ,SAAU,EAArG;AACH,WANoB,CAQrB;AACA;AACA;AACA;;AACH;;AAEDC,QAAAA,MAAM,GAAG;AACL;AAAA;AAAA,4CAAaC,EAAb,CAAgB,mBAAhB,EAAqC,KAAKC,aAAL,CAAmBC,IAAnB,CAAwB,IAAxB,CAArC;AACA;AAAA;AAAA,4CAAaF,EAAb,CAAgB,kBAAhB,EAAoC,KAAKG,YAAL,CAAkBD,IAAlB,CAAuB,IAAvB,CAApC;AAEA,gBAAME,OAAO,GAAG,CAAhB;AACA,gBAAMC,OAAO,GAAG,CAAhB;AACA,gBAAMC,MAAM,GAAG,GAAf;AACA,gBAAMC,SAAS,GAAG,EAAlB;AAEA,gBAAMC,UAAU,GAAG,KAAKd,IAAL,CAAUe,cAAV,CAAyB,SAAzB,CAAnB;;AAEA,eAAK,IAAIC,CAAC,GAAGH,SAAb,EAAwBG,CAAC,GAAG,CAA5B,EAA+BA,CAAC,EAAhC,EAAoC;AAChC,kBAAMC,KAAK,GAAID,CAAC,GAAGH,SAAL,GAAkBK,IAAI,CAACC,EAAvB,GAA4B,CAA5B,GAAgCD,IAAI,CAACC,EAAL,GAAU,CAAxD,CADgC,CAC4B;;AAC5D,kBAAMC,CAAC,GAAGV,OAAO,GAAGE,MAAM,GAAGM,IAAI,CAACG,GAAL,CAASJ,KAAT,CAA7B;AACA,kBAAMK,CAAC,GAAGX,OAAO,GAAGC,MAAM,GAAGM,IAAI,CAACK,GAAL,CAASN,KAAT,CAA7B;AAEA,kBAAMO,OAAO,GAAGjD,WAAW,CAAC,KAAKkD,GAAN,CAA3B;AACAD,YAAAA,OAAO,CAACE,WAAR,CAAoBN,CAApB,EAAuBE,CAAvB;AACAR,YAAAA,UAAU,CAACa,QAAX,CAAoBH,OAApB,EAPgC,CAShC;AACA;AACA;AACA;AACH;AAEJ;;AACDjB,QAAAA,aAAa,GAAG;AACZ,eAAKZ,OAAL,GAAe,EAAf;AACA,eAAKiC,aAAL,CAAmBC,MAAnB,GAA4B,KAA5B;AACA,eAAKC,IAAL,CAAUD,MAAV,GAAmB,IAAnB;AACA,eAAK7B,IAAL,CAAUC,cAAV,CAAyB,kBAAzB,EAA6C4B,MAA7C,GAAsD,KAAtD;AACA,eAAKnC,QAAL,CAAcqC,OAAd,CAAsBP,OAAO,IAAI;AAC7BzC,YAAAA,KAAK,CAACiD,eAAN,CAAsBR,OAAtB;AACAA,YAAAA,OAAO,CAACS,IAAR;AACH,WAHD;AAKA,eAAKjC,IAAL,CAAUC,cAAV,CAAyB,eAAzB,EAA0CC,YAA1C,CAAuD1B,KAAvD,EAA8D2B,MAA9D,GAAuE,MAAvE;AACA,eAAKH,IAAL,CAAUC,cAAV,CAAyB,aAAzB,EAAwCC,YAAxC,CAAqD1B,KAArD,EAA4D2B,MAA5D,GAAsE,MAAK,KAAKP,UAAL,CAAgBQ,SAAU,EAArG;AACH;;AACDK,QAAAA,YAAY,GAAG;AACX,eAAKf,QAAL,CAAcqC,OAAd,CAAsBP,OAAO,IAAI;AAC7BzC,YAAAA,KAAK,CAACiD,eAAN,CAAsBR,OAAtB;AACAA,YAAAA,OAAO,CAACS,IAAR;AACH,WAHD;AAIA,eAAKtC,OAAL,GAAe,EAAf;AACA,eAAKiC,aAAL,CAAmBC,MAAnB,GAA4B,KAA5B;AACA,eAAKC,IAAL,CAAUD,MAAV,GAAmB,KAAnB;AACA,eAAK7B,IAAL,CAAUC,cAAV,CAAyB,kBAAzB,EAA6C4B,MAA7C,GAAsD,KAAtD;AAEA,eAAK7B,IAAL,CAAUC,cAAV,CAAyB,eAAzB,EAA0CC,YAA1C,CAAuD1B,KAAvD,EAA8D2B,MAA9D,GAAuE,SAAvE;AACA,eAAKH,IAAL,CAAUC,cAAV,CAAyB,aAAzB,EAAwCC,YAAxC,CAAqD1B,KAArD,EAA4D2B,MAA5D,GAAsE,MAAK,KAAKP,UAAL,CAAgBsC,YAAa,EAAxG;AACH;;AAGMC,QAAAA,mBAAmB,GAAG;AACzB,eAAKP,aAAL,CAAmBC,MAAnB,GAA4B,IAA5B;AACA,eAAKD,aAAL,CAAmB1B,YAAnB,CAAgCrB,SAAhC,EAA2CuD,IAA3C;AACH;;AAIMC,QAAAA,MAAM,GAAG;AACZ,gBAAMC,OAAO,GAAG,KAAKtC,IAAL,CAAUC,cAAV,CAAyB,kBAAzB,EAA6CC,YAA7C,CAA0DpB,SAA1D,CAAhB,CADY,CAEZ;AACA;;AAEAwD,UAAAA,OAAO,CAACA,OAAR,GAAkB,GAAlB;AACAA,UAAAA,OAAO,CAACtC,IAAR,CAAauC,KAAb,GAAqB,IAAI3D,IAAJ,CAAS,GAAT,EAAc,GAAd,CAArB;AACA0D,UAAAA,OAAO,CAACtC,IAAR,CAAa6B,MAAb,GAAsB,IAAtB;AAEAlD,UAAAA,KAAK,CAAC2D,OAAO,CAACtC,IAAT,CAAL,CACKwC,EADL,CACQ,GADR,EACa;AAAED,YAAAA,KAAK,EAAE,IAAI3D,IAAJ,CAAS,GAAT,EAAc,GAAd;AAAT,WADb,EAEK4D,EAFL,CAEQ,GAFR,EAEa;AAAED,YAAAA,KAAK,EAAE,IAAI3D,IAAJ,CAAS,CAAT,EAAY,CAAZ;AAAT,WAFb,EAGK6D,IAHL,CAGU9D,KAAK,CAAC2D,OAAD,CAAL,CAAeE,EAAf,CAAkB,CAAlB,EAAqB;AAAEF,YAAAA,OAAO,EAAE;AAAX,WAArB,CAHV,EAIKI,KAJL,GAKKC,KALL;AAMH;;AAGMC,QAAAA,UAAU,GAAG;AAChB,eAAKC,GAAL,CAAShB,MAAT,GAAkB,KAAlB;AACA,eAAKC,IAAL,CAAUD,MAAV,GAAmB,IAAnB;AACA,eAAKD,aAAL,CAAmBC,MAAnB,GAA4B,KAA5B;AACA,eAAK7B,IAAL,CAAUC,cAAV,CAAyB,kBAAzB,EAA6C4B,MAA7C,GAAsD,KAAtD;AACH;;AAEMiB,QAAAA,sBAAsB,CAACC,MAAD,EAAiBC,QAAjB,EAAwC;AACjE,eAAKrD,OAAL,CAAasD,IAAb,CAAkBF,MAAlB;AACA,eAAKnB,aAAL,CAAmBC,MAAnB,GAA4B,IAA5B,CAFiE,CAGjE;;AAEA,gBAAMqB,OAAO,GAAG,KAAKlD,IAAL,CAAUC,cAAV,CAAyB,kBAAzB,CAAhB;AACA,gBAAMkD,cAAc,GAAGD,OAAO,CAAChD,YAAR,CAAqBpB,SAArB,CAAvB;AACAoE,UAAAA,OAAO,CAAChD,YAAR;AAAA;AAAA,oCAA+BkD,SAA/B,CAAyCL,MAAzC;AAEAhE,UAAAA,KAAK,CAACiD,eAAN,CAAsBkB,OAAtB;AACAnE,UAAAA,KAAK,CAACiD,eAAN,CAAsBmB,cAAtB;AAEAA,UAAAA,cAAc,CAACb,OAAf,GAAyB,GAAzB;AACAY,UAAAA,OAAO,CAACX,KAAR,GAAgB,IAAI3D,IAAJ,CAAS,CAAT,EAAY,CAAZ,CAAhB;AACAsE,UAAAA,OAAO,CAACrB,MAAR,GAAiB,IAAjB;AAEAlD,UAAAA,KAAK,CAACuE,OAAD,CAAL,CACKG,KADL,CACW,GADX,EAEKb,EAFL,CAEQ,IAFR,EAEc;AAAED,YAAAA,KAAK,EAAE,IAAI3D,IAAJ,CAAS,GAAT,EAAc,GAAd;AAAT,WAFd,EAGK4D,EAHL,CAGQ,IAHR,EAGc;AAAED,YAAAA,KAAK,EAAE,IAAI3D,IAAJ,CAAS,CAAT,EAAY,CAAZ;AAAT,WAHd,EAIK0E,IAJL,CAIU,MAAM;AACR,iBAAK5D,QAAL,CAAc,KAAKC,OAAL,CAAa4D,MAAb,GAAsB,CAApC,EAAuCC,KAAvC;;AACA,gBAAIR,QAAJ,EAAc;AACVA,cAAAA,QAAQ;AACX;AACJ,WATL,EAUKK,KAVL,CAUW,GAVX,EAWI;AAXJ,WAYKX,KAZL,GAaKY,IAbL,CAaU,MAAM;AACR,gBAAI,KAAK3D,OAAL,CAAa4D,MAAb,IAAuB,EAA3B,EAA+B;AAC3B,mBAAK3B,aAAL,CAAmBC,MAAnB,GAA4B,KAA5B;AACH,aAFD,MAEO;AACHsB,cAAAA,cAAc,CAACb,OAAf,GAAyB,CAAzB;AACAY,cAAAA,OAAO,CAACrB,MAAR,GAAiB,KAAjB;AACH;AACJ,WApBL,EAqBKc,KArBL;AAwBA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGK;AAID;AACJ;AACA;AACA;;;AACIc,QAAAA,SAAS,CAACC,OAAD,EAAkB;AACvB;AACA,cAAIA,OAAO,IAAI,EAAf,EAAmB;AACf,iBAAKC,kBAAL;AACH,WAJsB,CAMvB;;;AACA,cAAID,OAAO,IAAI,CAAX,IAAgBA,OAAO,GAAG,EAA9B,EAAkC;AAC9B,iBAAKE,IAAL;AACH;;AAED,cAAKF,OAAO,GAAG,CAAX,IAAkBA,OAAO,GAAG,CAAX,IAAiB,CAAtC,EAAyC;AACrC;AAAA;AAAA,0CAAWG,eAAX,CAA2B,KAAK/B,IAAhC,EAAsC,IAAtC,EAA4C,GAA5C,EAAiD,CAAjD;AACH;;AACD,cAAI4B,OAAO,IAAI,CAAf,EAAkB;AACd,iBAAKhE,QAAL,CAAcqC,OAAd,CAAsBN,GAAG,IAAI;AACzBA,cAAAA,GAAG,CAACvB,YAAJ;AAAA;AAAA,8BAAsB+B,IAAtB;AACAR,cAAAA,GAAG,CAACzB,IAAJ,CAAS8D,QAAT,CAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAxB;AACH,aAHD;AAIH;;AAED,gBAAMC,QAAQ,GAAG;AAAA;AAAA,0CAAYC,cAAZ,CAA2BN,OAA3B,CAAjB;AACA,eAAKO,cAAL,CAAoB9D,MAApB,GAA6B4D,QAA7B;AAEH;AAED;AACJ;AACA;;;AACWH,QAAAA,IAAI,GAAG;AACV,eAAKlE,QAAL,CAAcqC,OAAd,CAAsB/B,IAAI,IAAI;AAC1BjB,YAAAA,KAAK,CAACiD,eAAN,CAAsBhC,IAAtB;AACH,WAFD;AAGA,gBAAMkE,IAAI,GAAG;AACTC,YAAAA,KAAK,EAAE;AADE,WAAb;AAGAxF,UAAAA,KAAK,CAACuF,IAAD,CAAL,CACK1B,EADL,CACQ,GADR,EACa;AAAE2B,YAAAA,KAAK,EAAE;AAAT,WADb,EAEKb,IAFL,CAEU,MAAM;AACR,iBAAK5D,QAAL,CAAc0E,GAAd,CAAkB3C,GAAG,IAAIA,GAAG,CAAC+B,KAAJ,EAAzB;AACH,WAJL,EAKKhB,EALL,CAKQ,GALR,EAKa;AAAE2B,YAAAA,KAAK,EAAE;AAAT,WALb,EAMKb,IANL,CAMU,MAAM;AACR,iBAAK5D,QAAL,CAAc0E,GAAd,CAAkB3C,GAAG,IAAIA,GAAG,CAACQ,IAAJ,EAAzB;AACH,WARL,EASKS,KATL,GAUKC,KAVL;AAWH;AAED;AACJ;AACA;;;AACYgB,QAAAA,kBAAkB,GAAG;AACzB;AACA,gBAAMU,YAAY,GAAG,EAArB;;AACA,eAAK,IAAIrD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKvB,UAAzB,EAAqCuB,CAAC,EAAtC,EAA0C;AACtCqD,YAAAA,YAAY,CAACpB,IAAb,CAAkB,CAAC,KAAKzD,YAAL,GAAoBwB,CAArB,IAA0B,KAAKtB,QAAL,CAAc6D,MAA1D;AACH,WALwB,CAOzB;;;AACA,gBAAMe,YAAY,GAAG,EAArB;;AACA,eAAK,IAAItD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKvB,UAAzB,EAAqCuB,CAAC,EAAtC,EAA0C;AACtC,kBAAMuD,KAAK,GAAG,CAAC,KAAK/E,YAAL,GAAoB,KAAKC,UAAzB,GAAsCuB,CAAtC,GAA0C,KAAKtB,QAAL,CAAc6D,MAAzD,IAAmE,KAAK7D,QAAL,CAAc6D,MAA/F;AACAe,YAAAA,YAAY,CAACrB,IAAb,CAAkBsB,KAAlB;AACH,WAZwB,CAczB;;;AACAD,UAAAA,YAAY,CAACvC,OAAb,CAAqB,CAACwC,KAAD,EAAQvD,CAAR,KAAc;AAC/BrC,YAAAA,KAAK,CAAC,KAAKe,QAAL,CAAc6E,KAAd,EAAqBvE,IAAtB,CAAL,CACKqD,KADL,CACWrC,CAAC,GAAG,GADf,EACoB;AADpB,aAEKwB,EAFL,CAEQ,GAFR,EAEa;AAAED,cAAAA,KAAK,EAAE,IAAI3D,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf;AAAT,aAFb,EAE2C;AAAE4F,cAAAA,MAAM,EAAE;AAAV,aAF3C,EAGKlB,IAHL,CAGU,MAAM,KAAK5D,QAAL,CAAc6E,KAAd,EAAqBtC,IAArB,EAHhB,EAIKU,KAJL;AAKH,WAND,EAfyB,CAuBzB;;AACA0B,UAAAA,YAAY,CAACtC,OAAb,CAAqB,CAACwC,KAAD,EAAQvD,CAAR,KAAc;AAC/BrC,YAAAA,KAAK,CAAC,KAAKe,QAAL,CAAc6E,KAAd,EAAqBvE,IAAtB,CAAL,CACKqD,KADL,CACWrC,CAAC,GAAG,GADf,EACoB;AADpB,aAEKwB,EAFL,CAEQ,GAFR,EAEa;AAAED,cAAAA,KAAK,EAAE,IAAI3D,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf;AAAT,aAFb,EAE2C;AAAE4F,cAAAA,MAAM,EAAE;AAAV,aAF3C,EAGKlB,IAHL,CAGU,MAAM,KAAK5D,QAAL,CAAc6E,KAAd,EAAqBf,KAArB,EAHhB,EAIKb,KAJL;AAKH,WAND,EAxByB,CAgCzB;;AACA,eAAKnD,YAAL,GAAoB,CAAC,KAAKA,YAAL,GAAoB,KAAKC,UAA1B,IAAwC,KAAKC,QAAL,CAAc6D,MAA1E;AACH;;AAESkB,QAAAA,QAAQ,GAAS,CAE1B;;AAESC,QAAAA,SAAS,GAAS;AACxB,eAAKC,sBAAL;AACH;;AAGDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AA1TwC,O;;;;;iBAGrB,I;;;;;;;iBAGY,I;;;;;;;iBAGX,I;;;;;;;iBAGD,I;;;;;;;iBAGU,I", "sourcesContent": ["import { _decorator, Component, easing, instantiate, Label, Node, Prefab, tween, UITransform, Vec3, Animation, UIOpacity, Tween } from 'cc';\r\nimport { Led } from './perfabs/Led';\r\nimport { TweenUtils } from '../framework/utils/TweenUtils';\r\nimport { FormatUtils } from '../framework/utils/FormatUtils';\r\nimport { NumberUtils } from '../framework/utils/NumberUtils';\r\nimport { BallItem } from './perfabs/BallItem';\r\nimport { eventManager } from '../framework/event/EventManager';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('NumberSpawner')\r\nexport class NumberSpawner extends Component {\r\n\r\n    @property(Prefab)\r\n    private led: Node = null;\r\n\r\n    @property(Label)\r\n    private countdownLabel: Label = null;\r\n\r\n    @property(Node)\r\n    private logo: Node = null;\r\n\r\n    @property(Node)\r\n    private bar: Node = null;\r\n\r\n    @property(Node)\r\n    private animationNode: Node = null;\r\n\r\n    private currentIndex: number = 0;\r\n    private lightCount: number = 3;\r\n\r\n    private ledNodes: Led[] = [];\r\n\r\n    private numbers: number[] = [];\r\n\r\n    private dataSource: any = null;\r\n\r\n    setDataSource(data: any) {\r\n        this.dataSource = data;\r\n\r\n        if (data.command == 'ColorLuckyRecentResult') {\r\n            this.node.getChildByPath('/current/next').getComponent(Label).string = 'Next';\r\n            this.node.getChildByPath('/current/sn').getComponent(Label).string = `SN-${this.dataSource.nextRound}`;\r\n        }\r\n        \r\n        // else {\r\n        //     this.node.getChildByPath('/current/next').getComponent(Label).string = 'Current';\r\n        //     this.node.getChildByPath('/current/sn').getComponent(Label).string = `SN-${this.dataSource.currentRound}`;\r\n        // }\r\n    }\r\n\r\n    onLoad() {\r\n        eventManager.on(\"BallsPanel_Finish\", this.onBallsFinish.bind(this));\r\n        eventManager.on(\"BallsPanel_Start\", this.onBallsStart.bind(this));\r\n\r\n        const centerX = 0;\r\n        const centerY = 0;\r\n        const radius = 210;\r\n        const numLights = 20;\r\n\r\n        const circleNode = this.node.getChildByName('current');\r\n\r\n        for (let i = numLights; i > 0; i--) {\r\n            const angle = (i / numLights) * Math.PI * 2 + Math.PI / 2;  // 将圆分成 20 等份 第一个节点在最上面\r\n            const x = centerX + radius * Math.cos(angle);\r\n            const y = centerY + radius * Math.sin(angle);\r\n\r\n            const ledNode = instantiate(this.led);\r\n            ledNode.setPosition(x, y);\r\n            circleNode.addChild(ledNode);\r\n\r\n            // 初始化所有灯为暗态\r\n            // const ledComponent = ledNode.getComponent(Led);\r\n            // ledComponent.dark();\r\n            // this.ledNodes.push(ledComponent);\r\n        }\r\n\r\n    }\r\n    onBallsFinish() {\r\n        this.numbers = [];\r\n        this.animationNode.active = false;\r\n        this.logo.active = true;\r\n        this.node.getChildByPath('/current/bigBall').active = false;\r\n        this.ledNodes.forEach(ledNode => {\r\n            Tween.stopAllByTarget(ledNode);\r\n            ledNode.dark();\r\n        });\r\n\r\n        this.node.getChildByPath('/current/next').getComponent(Label).string = 'Next';\r\n        this.node.getChildByPath('/current/sn').getComponent(Label).string = `SN-${this.dataSource.nextRound}`;\r\n    }\r\n    onBallsStart() {\r\n        this.ledNodes.forEach(ledNode => {\r\n            Tween.stopAllByTarget(ledNode);\r\n            ledNode.dark();\r\n        });\r\n        this.numbers = [];\r\n        this.animationNode.active = false;\r\n        this.logo.active = false;\r\n        this.node.getChildByPath('/current/bigBall').active = false;\r\n\r\n        this.node.getChildByPath('/current/next').getComponent(Label).string = 'Current';\r\n        this.node.getChildByPath('/current/sn').getComponent(Label).string = `SN-${this.dataSource.currentRound}`;\r\n    }\r\n\r\n\r\n    public playBounceAnimation() {\r\n        this.animationNode.active = true;\r\n        this.animationNode.getComponent(Animation).play();\r\n    }\r\n\r\n\r\n\r\n    public btnAni() {\r\n        const opacity = this.node.getChildByPath('/current/bigBall').getComponent(UIOpacity);\r\n        // node.setScale(new Vec3(0.2, 0.2));\r\n        // node.setPosition(new Vec3(0, -170, 0));\r\n\r\n        opacity.opacity = 255;\r\n        opacity.node.scale = new Vec3(0.2, 0.2);\r\n        opacity.node.active = true;\r\n\r\n        tween(opacity.node)\r\n            .to(0.2, { scale: new Vec3(1.2, 1.2) })\r\n            .to(0.2, { scale: new Vec3(1, 1) })\r\n            .then(tween(opacity).to(2, { opacity: 0 }))\r\n            .union()\r\n            .start();\r\n    }\r\n\r\n\r\n    public setDefault() {\r\n        this.bar.active = false;\r\n        this.logo.active = true;\r\n        this.animationNode.active = false;\r\n        this.node.getChildByPath('/current/bigBall').active = false;\r\n    }\r\n\r\n    public showBigNumberAnimation(number: number, callback?: () => void) {\r\n        this.numbers.push(number);\r\n        this.animationNode.active = true;\r\n        // this.animationNode.getComponent(Animation).resume();\r\n\r\n        const bigBall = this.node.getChildByPath('/current/bigBall');\r\n        const bigBallOpacity = bigBall.getComponent(UIOpacity);\r\n        bigBall.getComponent(BallItem).setNumber(number);\r\n\r\n        Tween.stopAllByTarget(bigBall);\r\n        Tween.stopAllByTarget(bigBallOpacity);\r\n\r\n        bigBallOpacity.opacity = 255;\r\n        bigBall.scale = new Vec3(0, 0);\r\n        bigBall.active = true;\r\n\r\n        tween(bigBall)\r\n            .delay(0.5)\r\n            .to(0.15, { scale: new Vec3(1.2, 1.2) })\r\n            .to(0.15, { scale: new Vec3(1, 1) })\r\n            .call(() => {\r\n                this.ledNodes[this.numbers.length - 1].light();\r\n                if (callback) {\r\n                    callback();\r\n                }\r\n            })\r\n            .delay(0.7)\r\n            // .then(tween(bigBallOpacity).to(0.2, { opacity: 0 }))\r\n            .union()\r\n            .call(() => {\r\n                if (this.numbers.length == 20) {\r\n                    this.animationNode.active = false;\r\n                } else {\r\n                    bigBallOpacity.opacity = 0;\r\n                    bigBall.active = false;\r\n                }\r\n            })\r\n            .start();\r\n\r\n\r\n        /*\r\n        if (this.numbers.length == 20) {\r\n            tween(bigBall)\r\n                .to(0.15, { scale: new Vec3(1.2, 1.2) })\r\n                .to(0.2, { scale: new Vec3(1, 1) })\r\n                .call(() => {\r\n\r\n                    if (callback) {\r\n                        callback();\r\n                    }\r\n\r\n                    this.tick();\r\n                })\r\n                .delay(1)\r\n                .union()\r\n                .call(() => {\r\n                    this.animationNode.active = false;\r\n                    // this.showBarAnimation(number);\r\n                })\r\n                .start();\r\n        } else {\r\n            tween(bigBall)\r\n                .to(0.15, { scale: new Vec3(1.2, 1.2) })\r\n                .to(0.2, { scale: new Vec3(1, 1) })\r\n                .call(() => {\r\n                    this.ledNodes[this.numbers.length - 1].light();\r\n                    if (callback) {\r\n                        callback();\r\n                    }\r\n                })\r\n                .delay(1)\r\n                .then(tween(bigBallOpacity).to(0.2, { opacity: 0 }))\r\n                .union()\r\n                .call(() => {\r\n                    bigBallOpacity.opacity = 0;\r\n                    bigBall.active = false;\r\n                })\r\n                .start();\r\n        }\r\n        */\r\n    }\r\n\r\n\r\n\r\n    /**\r\n     * 倒计时\r\n     * @param seconds \r\n     */\r\n    countdown(seconds: number) {\r\n        //流水灯显示\r\n        if (seconds >= 20) {\r\n            this.startFlowAnimation();\r\n        }\r\n\r\n        //  倒计时小于20秒时，开始闪烁led\r\n        if (seconds >= 1 && seconds < 20) {\r\n            this.tick();\r\n        }\r\n\r\n        if ((seconds > 0) && (seconds % 5) == 0) {\r\n            TweenUtils.scaleAndRestore(this.logo, 1.15, 0.3, 0);\r\n        }\r\n        if (seconds <= 0) {\r\n            this.ledNodes.forEach(led => { \r\n                led.getComponent(Led).dark();\r\n                led.node.setScale(1, 1, 1);\r\n            });\r\n        }\r\n\r\n        const duration = FormatUtils.formatDuration(seconds);\r\n        this.countdownLabel.string = duration;\r\n\r\n    }\r\n\r\n    /**\r\n     * 闪烁led\r\n     */\r\n    public tick() {\r\n        this.ledNodes.forEach(node => {\r\n            Tween.stopAllByTarget(node);\r\n        });\r\n        const objs = {\r\n            value: 0\r\n        };\r\n        tween(objs)\r\n            .to(0.4, { value: 0 })\r\n            .call(() => {\r\n                this.ledNodes.map(led => led.light());\r\n            })\r\n            .to(0.4, { value: 0 })\r\n            .call(() => {\r\n                this.ledNodes.map(led => led.dark());\r\n            })\r\n            .union()\r\n            .start();\r\n    }\r\n\r\n    /**\r\n     * 流水灯动画\r\n     */\r\n    private startFlowAnimation() {\r\n        // 计算当前要亮起的 3 个灯珠索引\r\n        const lightsToShow = [];\r\n        for (let i = 0; i < this.lightCount; i++) {\r\n            lightsToShow.push((this.currentIndex + i) % this.ledNodes.length);\r\n        }\r\n\r\n        // 计算上一轮亮起的 3 个灯珠（即当前 - lightCount 到当前 -1）\r\n        const lightsToHide = [];\r\n        for (let i = 0; i < this.lightCount; i++) {\r\n            const index = (this.currentIndex - this.lightCount + i + this.ledNodes.length) % this.ledNodes.length;\r\n            lightsToHide.push(index);\r\n        }\r\n\r\n        // 逐个熄灭之前的 3 个灯珠\r\n        lightsToHide.forEach((index, i) => {\r\n            tween(this.ledNodes[index].node)\r\n                .delay(i * 0.2) // 延迟熄灭时间，与点亮同步\r\n                .to(0.2, { scale: new Vec3(1, 1, 1) }, { easing: 'backIn' })\r\n                .call(() => this.ledNodes[index].dark())\r\n                .start();\r\n        });\r\n\r\n        // 逐个点亮新的 3 个灯珠\r\n        lightsToShow.forEach((index, i) => {\r\n            tween(this.ledNodes[index].node)\r\n                .delay(i * 0.2) // 依次点亮\r\n                .to(0.2, { scale: new Vec3(1, 1, 1) }, { easing: 'backOut' })\r\n                .call(() => this.ledNodes[index].light())\r\n                .start();\r\n        });\r\n\r\n        // 更新当前索引\r\n        this.currentIndex = (this.currentIndex + this.lightCount) % this.ledNodes.length;\r\n    }\r\n\r\n    protected onEnable(): void {\r\n\r\n    }\r\n\r\n    protected onDisable(): void {\r\n        this.unscheduleAllCallbacks();\r\n    }\r\n\r\n\r\n    update(deltaTime: number) {\r\n\r\n    }\r\n}\r\n\r\n\r\n"]}