export class FormatUtils {

    public static formatNumber(number: number): string {
        return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }


    /**
     * 格式化倒计时
     * @param totalSeconds 总秒数
     * @returns 
     */
    public static formatDuration(totalSeconds: number): string {
        if (totalSeconds <= 60) {
            return `${totalSeconds}`;
        }

        const padZero = (num: number) => num < 10 ? `0${num}` : num.toString();
        let hours = Math.floor(totalSeconds / 3600);
        let minutes = Math.floor((totalSeconds % 3600) / 60);
        let seconds = Math.floor(totalSeconds % 60);

        let format = ``;

        if (hours > 0) {
            format += `${padZero(hours)}:`;
        }

        if (minutes > 0) {
            format += `${padZero(minutes)}:`;
        }

        format += format.includes(':') ? `${padZero(seconds)}` : seconds;

        return format;
    }
}