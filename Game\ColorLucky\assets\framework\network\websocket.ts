import logger from "../logger/logger";

export type WebSocketOptions = {
    url: string;
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
    heartbeatInterval?: number;
    heartbeat?: () => any
}

class FrameWebSocket {

    private ws!: WebSocket;

    //配置信息
    private options: WebSocketOptions;

    //是否允许重连
    private allowReconnect = true;

    //重连锁
    private lockReconnect = false;

    //当前重连次数
    private totalReconnectAttempts = 0;

    //心跳线程
    private heartbeatRunner: any;

    //丢失心跳次数
    private lossHeartbeatCount = 0;

    //未发送的消息队列 在onOpen时会一次性发出
    private messageQueue: Array<any> = [];

    //事件监听器
    private listeners: { [key: string]: Array<Function> } = {};

    public constructor(options: WebSocketOptions) {
        this.options = options;

        if (!options.reconnectInterval) {
            options.reconnectInterval = 3 * 1000;
        }

        if (!options.heartbeatInterval) {
            options.heartbeatInterval = 30 * 1000;
        }

        if (!options.maxReconnectAttempts) {
            options.maxReconnectAttempts = 0;
        }

        if (!options.heartbeat) {
            options.heartbeat = () => ({
                command: 'ping',
                clientTimestamp: Date.now()
            });
        }

        this.init(options);
    }

    public init(options: WebSocketOptions) {
        try {
            this.ws = new WebSocket(options.url);
            this.ws.onopen = this.onOpen.bind(this);
            this.ws.onclose = this.onClose.bind(this);
            this.ws.onerror = this.onError.bind(this);
            this.ws.onmessage = this.handleMessage.bind(this);
        } catch (ex) {
            logger.error(ex);
        }
    }

    private onOpen(ev: any) {
        //重置丢失心跳次数
        this.lossHeartbeatCount = 0;
        //连接成功一次重置重连次数
        this.totalReconnectAttempts = 0;
        //开始心跳
        this.startHeartbeat();

        //发送队列中的消息
        while (this.messageQueue.length > 0) {
            let message = this.messageQueue.shift();
            this.send(message);
        }
    }

    private onClose(ev: any) {
        this.reconnect();
    }

    private onError(ev: any) {
        this.reconnect();
    }

    // 注册事件监听器
    public on(event: string, callback: Function) {
        if (!this.listeners[event]) {
            this.listeners[event] = [];
        }
        this.listeners[event].push(callback);
    }

    // 移除事件监听器
    public off(event: string, callback: Function) {
        if (this.listeners[event]) {
            this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
        }
    }

    // 触发事件
    private emit(event: string, data: any) {
        if (this.listeners[event]) {
            this.listeners[event].forEach(callback => callback(data));
        }
    }

    private handleMessage(ev: MessageEvent<any>) {
        //客户端收到消息即重置丢失心跳次数
        this.lossHeartbeatCount = 0;
        try {
            const parsedData = JSON.parse(ev.data);
            logger.debug("handleMessage", parsedData);
            // 触发外部监听器
            this.emit('message', parsedData);
        } catch (ex) {
            logger.error("Failed to parse message", ex);
        }
    }

    private startHeartbeat() {
        if (this.ws.readyState != WebSocket.OPEN) {
            this.clearHeartbeat();
            return;
        }

        this.heartbeatRunner = setInterval(() => {
            if (this.ws.readyState != WebSocket.OPEN) {
                return;
            }
            this.lossHeartbeatCount++;

            //超过3次丢失心跳 关闭连接
            if (this.lossHeartbeatCount >= 3) {
                this.ws.close();
            }

            this.send(this.options.heartbeat!());

        }, this.options.heartbeatInterval);
    }

    private clearHeartbeat() {
        this.lossHeartbeatCount = 0;
        clearInterval(this.heartbeatRunner);
        this.heartbeatRunner = undefined;
    }

    public send(message: any) {
        if (this.ws.readyState != WebSocket.OPEN) {
            this.messageQueue.push(message);
            return;
        }

        try {
            this.ws.send(JSON.stringify(message));
        } catch (ex) {
            logger.error(ex);
        }
    }

    private reconnect() {
        this.clearHeartbeat();

        if (!this.allowReconnect) {
            return;
        }

        if (this.lockReconnect) {
            return;
        }

        //超过最大重连次数 不在重连
        if (this.options.maxReconnectAttempts! > 0 &&
            this.totalReconnectAttempts >= this.options.maxReconnectAttempts!) {
            this.allowReconnect = false;
            return;
        }

        //重连中锁定,
        this.lockReconnect = true;
        this.totalReconnectAttempts++;

        setTimeout(() => {
            logger.info("正在重连...重连次数", this.totalReconnectAttempts);

            this.init(this.options);

            this.lockReconnect = false;
        }, this.options.reconnectInterval);
    }

    public close() {
        try {
            this.ws.close();
            this.allowReconnect = false;
        } catch (ex) {
            logger.error(ex);
        }
    }
}

export default FrameWebSocket;