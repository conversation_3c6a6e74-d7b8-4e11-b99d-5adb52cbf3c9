import { _decorator, Component, instantiate, Label, Node, Prefab, Animation, tween, UITransform, easing } from 'cc';
import { NumberUtils } from '../../framework/utils/NumberUtils';
import { BallItem } from '../perfabs/BallItem';
import { TweenUtils } from '../../framework/utils/TweenUtils';
import { eventManager } from '../../framework/event/EventManager';
import { NumberSpawner } from '../NumberSpawner';
const { ccclass, property } = _decorator;

@ccclass('BallsPanel')
export class BallsPanel extends Component {



    @property(Prefab)
    private hole: Node = null;


    @property(Node)
    private bar: Node = null;


    private numberSpawner: NumberSpawner = null;

    private dataSource: any = null;

    private numbers: Array<number> = [];

    setDataSource(data: any, numberSpawner: NumberSpawner) {
        console.log("BallsPanel setDataSource", data);
        this.dataSource = data;
        this.numberSpawner = numberSpawner;
    }

    private displayBalls() {
        // this.dataSource.currentNumber = NumberUtils.getRandomNumbers(20);
        this.numbers = [];
        const delay = 1.5;
        let numberIdx = 0;
        // this.numberSpawner.playBounceAnimation();
        this.schedule(() => {

            //小球数字
            const number = this.dataSource.currentNumber[numberIdx];
            this.numbers.push(number);

            console.log("number", number);

            this.numberSpawner.showBigNumberAnimation(number, this.onBigNumberAnimationend.bind(this, number));

            numberIdx++;

            // this.updateBarAnimation();

            if (numberIdx >= 19) {
                this.scheduleOnce(() => {
                    eventManager.emit('BallsPanel_Finish');
                    // TweenUtils.fadeOut(this.node);
                }, 5);
            }
        }, delay, 19, delay);
    }

    private onBigNumberAnimationend(number: number) {
        //小球坑位
        const hole = this.node.getChildByPath('numbers').children[number - 1];
        TweenUtils.fadeIn(hole.getChildByName('ball'), 0.1);
        hole.getChildByName('ani').active = true;

        const ani = hole.getChildByName('ani').getComponent(Animation);
        ani.play('star');
        this.updateBarAnimation();
    }


    private hideAllBalls() {
        this.node.getChildByPath('numbers').children.forEach(node => {
            node.getChildByName('ball').active = false;
            node.getChildByName('ani').active = false;
        });
    }


    private updateBarAnimation() {

        const colors = NumberUtils.getColorCount(this.numbers);

        let gap = 12.5;

        //当小球个数>10个时所有柱体按照比例增加
        if (this.numbers.length == 20) {
            gap = 250 / Math.max(...colors);
        } else if (this.numbers.length > 10) {
            gap = 250 / ((20 - this.numbers.length) + Math.max(...colors));
        }

        this.bar.children.forEach((barItem, idx) => {
            const countNode = barItem.getChildByName('text').getComponent(Label);
            countNode.string = colors[idx].toString();

            const targetNode = barItem.getChildByName('value').getComponent(UITransform);

            // 创建 Tween 动画
            tween(targetNode)
                .to(0.1, { height: colors[idx] * gap }, {
                    easing: easing.linear,
                })
                .start();
        });
    }


    protected onEnable(): void {
        eventManager.emit('BallsPanel_Start');
        this.displayBalls();
    }

    protected onDisable(): void {
        this.numbers = [];
        this.unscheduleAllCallbacks();
        this.updateBarAnimation();
        this.hideAllBalls();
    }

    onLoad() {
        console.log("BallsPanel start");
        Array.from({ length: 80 }).forEach((_, i) => {
            const hole = instantiate(this.hole);
            hole.getChildByName('text').getComponent(Label).string = (i + 1).toString();
            hole.getChildByName('ball').getComponent(BallItem).setNumber(i + 1);
            this.node.getChildByPath('numbers').addChild(hole);
        });
    }

    update(deltaTime: number) {

    }
}


