System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, TweenUtils, _dec, _class, _crd, ccclass, property, Pearl;

  function _reportPossibleCrUseOfTweenUtils(extras) {
    _reporterNs.report("TweenUtils", "../../framework/utils/TweenUtils", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      TweenUtils = _unresolved_2.TweenUtils;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "ce7aa+fHSZEm6iJx8N+55S5", "Pearl", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("Pearl", Pearl = (_dec = ccclass('Pearl'), _dec(_class = class Pearl extends Component {
        start() {}

        update(deltaTime) {}

        light() {
          (_crd && TweenUtils === void 0 ? (_reportPossibleCrUseOfTweenUtils({
            error: Error()
          }), TweenUtils) : TweenUtils).fadeIn(this.node.getChildByName('light'), 0.1); // this.node.getChildByName('light').active = true;
        }

        dark() {
          (_crd && TweenUtils === void 0 ? (_reportPossibleCrUseOfTweenUtils({
            error: Error()
          }), TweenUtils) : TweenUtils).fadeOut(this.node.getChildByName('light'), 0.1); // this.node.getChildByName('light').active = false;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e138bddf128dfb655e2c91b580726d35e3e093a7.js.map