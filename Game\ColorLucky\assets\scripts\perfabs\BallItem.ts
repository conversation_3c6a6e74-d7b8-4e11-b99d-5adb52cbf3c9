import { _decorator, Component, Label, Node, Sprite } from 'cc';
import { NumberUtils } from '../../framework/utils/NumberUtils';
const { ccclass, property } = _decorator;

@ccclass('BallItem')
export class BallItem extends Component {

    start() {

    }

    public setNumber(number: number) {

        this.node.getChildByName('text').getComponent(Label).string = number.toString();
        const color = NumberUtils.getColor(number);

        this.node.getChildByPath('/bg').children.forEach((item, index) => {
            item.active = (index == color);
        });
    }

    update(deltaTime: number) {
        
    }
}


